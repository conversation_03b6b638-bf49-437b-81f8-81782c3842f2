import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import SettlementData, Base
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SettlementDataExporter:
    def __init__(self, database_url="mysql+pymysql://root:test123@localhost/energy_db"):
        """
        Initialize the Settlement Data Exporter
        
        Args:
            database_url (str): Database connection URL
        """
        self.engine = create_engine(database_url)
        self.Session = sessionmaker(bind=self.engine)
        
    def export_to_csv(self, output_file="settlement_data_full.csv"):
        """
        Export all settlement data to CSV file
        
        Args:
            output_file (str): Output CSV file name/path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Starting settlement data export to CSV...")
            
            session = self.Session()
            
            # Query all settlement data
            query = session.query(SettlementData).order_by(
                SettlementData.date.desc(),
                SettlementData.time.desc(),
                SettlementData.client_name,
                SettlementData.plant_id
            )
            
            settlement_records = query.all()
            logger.info(f"Found {len(settlement_records)} settlement records")
            
            if not settlement_records:
                logger.warning("No settlement data found to export")
                session.close()
                return False
            
            # Convert to list of dictionaries
            data_list = []
            for record in settlement_records:
                data_dict = {
                    'id': record.id,
                    'plant_id': record.plant_id,
                    'client_name': record.client_name,
                    'cons_unit': record.cons_unit,
                    'type': record.type,
                    'date': record.date,
                    'time': record.time,
                    'datetime': record.datetime,
                    'allocated_generation': float(record.allocated_generation) if record.allocated_generation else 0.0,
                    'consumption': float(record.consumption) if record.consumption else 0.0,
                    'deficit': float(record.deficit) if record.deficit else 0.0,
                    'surplus_demand': float(record.surplus_demand) if record.surplus_demand else 0.0,
                    'surplus_generation': float(record.surplus_generation) if record.surplus_generation else 0.0,
                    'settled': float(record.settled) if record.settled else 0.0,
                    'slot_name': record.slot_name,
                    'slot_time': record.slot_time
                }
                data_list.append(data_dict)
            
            # Create DataFrame
            df = pd.DataFrame(data_list)
            
            # Export to CSV
            df.to_csv(output_file, index=False)
            
            session.close()
            
            # Get file size for confirmation
            file_size = os.path.getsize(output_file)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"✅ Successfully exported {len(settlement_records)} records to '{output_file}'")
            logger.info(f"📁 File size: {file_size_mb:.2f} MB")
            logger.info(f"📍 File location: {os.path.abspath(output_file)}")
            
            # Display column information
            logger.info(f"📊 CSV contains {len(df.columns)} columns:")
            for col in df.columns:
                logger.info(f"   - {col}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting settlement data: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def export_filtered_to_csv(self, output_file="settlement_data_filtered.csv", 
                             start_date=None, end_date=None, client_name=None, 
                             plant_id=None, cons_unit=None):
        """
        Export filtered settlement data to CSV file
        
        Args:
            output_file (str): Output CSV file name/path
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            plant_id (str): Filter by plant ID (optional)
            cons_unit (str): Filter by consumption unit (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Starting filtered settlement data export to CSV...")
            
            session = self.Session()
            
            # Build query with filters
            query = session.query(SettlementData)
            
            if start_date:
                query = query.filter(SettlementData.date >= start_date)
                logger.info(f"Filter: start_date >= {start_date}")
            
            if end_date:
                query = query.filter(SettlementData.date <= end_date)
                logger.info(f"Filter: end_date <= {end_date}")
            
            if client_name:
                query = query.filter(SettlementData.client_name == client_name)
                logger.info(f"Filter: client_name = {client_name}")
            
            if plant_id:
                query = query.filter(SettlementData.plant_id == plant_id)
                logger.info(f"Filter: plant_id = {plant_id}")
            
            if cons_unit:
                query = query.filter(SettlementData.cons_unit == cons_unit)
                logger.info(f"Filter: cons_unit = {cons_unit}")
            
            # Order results
            query = query.order_by(
                SettlementData.date.desc(),
                SettlementData.time.desc(),
                SettlementData.client_name,
                SettlementData.plant_id
            )
            
            settlement_records = query.all()
            logger.info(f"Found {len(settlement_records)} settlement records matching filters")
            
            if not settlement_records:
                logger.warning("No settlement data found matching the filters")
                session.close()
                return False
            
            # Convert to list of dictionaries
            data_list = []
            for record in settlement_records:
                data_dict = {
                    'id': record.id,
                    'plant_id': record.plant_id,
                    'client_name': record.client_name,
                    'cons_unit': record.cons_unit,
                    'type': record.type,
                    'date': record.date,
                    'time': record.time,
                    'datetime': record.datetime,
                    'allocated_generation': float(record.allocated_generation) if record.allocated_generation else 0.0,
                    'consumption': float(record.consumption) if record.consumption else 0.0,
                    'deficit': float(record.deficit) if record.deficit else 0.0,
                    'surplus_demand': float(record.surplus_demand) if record.surplus_demand else 0.0,
                    'surplus_generation': float(record.surplus_generation) if record.surplus_generation else 0.0,
                    'settled': float(record.settled) if record.settled else 0.0,
                    'slot_name': record.slot_name,
                    'slot_time': record.slot_time
                }
                data_list.append(data_dict)
            
            # Create DataFrame
            df = pd.DataFrame(data_list)
            
            # Export to CSV
            df.to_csv(output_file, index=False)
            
            session.close()
            
            # Get file size for confirmation
            file_size = os.path.getsize(output_file)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"✅ Successfully exported {len(settlement_records)} filtered records to '{output_file}'")
            logger.info(f"📁 File size: {file_size_mb:.2f} MB")
            logger.info(f"📍 File location: {os.path.abspath(output_file)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting filtered settlement data: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """
    Main function to run the settlement data export
    """
    exporter = SettlementDataExporter()
    
    # Export all data
    print("="*60)
    print("SETTLEMENT DATA EXPORT")
    print("="*60)
    
    success = exporter.export_to_csv("settlement_data.csv")
    
    if success:
        print("\n🎉 Export completed successfully!")
        print("📄 Check 'settlement_data.csv' file in the current directory")
    else:
        print("\n❌ Export failed. Check the logs for details.")
    
    # Example of filtered export (uncomment to use)
    # print("\n" + "="*60)
    # print("FILTERED EXPORT EXAMPLE")
    # print("="*60)
    # 
    # # Export data for specific date range
    # success = exporter.export_filtered_to_csv(
    #     output_file="settlement_data_filtered.csv",
    #     start_date="2024-01-01",
    #     end_date="2024-01-31",
    #     client_name="Client1"  # Replace with actual client name
    # )

if __name__ == "__main__":
    main()