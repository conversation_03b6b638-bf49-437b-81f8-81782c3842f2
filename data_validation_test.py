#!/usr/bin/env python3
"""
Data Validation Test for Banking Settlement Initial Aggregated Values
Acting as an evaluator to verify data correctness
"""

from sqlalchemy import create_engine, func, and_, or_
from sqlalchemy.orm import sessionmaker
from decimal import Decimal
import logging
from models import SettlementData, BankingSettlement, Base
from datetime import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define time slots for validation
TIME_SLOTS = {
    'Morning Peak': {'start_time': time(6, 0), 'end_time': time(9, 0)},
    'Day': {'start_time': time(9, 0), 'end_time': time(18, 0)},
    'Evening Peak': {'start_time': time(18, 0), 'end_time': time(22, 0)},
    'Night': {'start_time': time(22, 0), 'end_time': time(6, 0)}
}

class DataValidator:
    def __init__(self, db_session):
        self.session = db_session
    
    def get_slot_name_from_time(self, time_obj: time) -> str:
        """Determine slot name from time - same logic as main code"""
        for slot_name, slot_info in TIME_SLOTS.items():
            start_time = slot_info['start_time']
            end_time = slot_info['end_time']
            
            if start_time > end_time:  # Crosses midnight
                if time_obj >= start_time or time_obj < end_time:
                    return slot_name
            else:  # Normal slot within same day
                if start_time <= time_obj < end_time:
                    return slot_name
        return 'Unknown'
    
    def validate_aggregated_values(self, year: int, month: int) -> dict:
        """
        Validate that the aggregated values in banking_settlement match 
        the source data from settlement_data
        """
        logger.info(f"VALIDATING DATA FOR {year}-{month:02d}")
        
        date_str = f"{year:04d}-{month:02d}"
        
        # Get banking settlement records for this month
        banking_records = self.session.query(BankingSettlement).filter(
            BankingSettlement.date == date_str
        ).all()
        
        if not banking_records:
            return {
                'status': 'NO_DATA',
                'message': f'No banking settlement data found for {date_str}'
            }
        
        # Get source settlement data for this month
        settlement_records = self.session.query(SettlementData).filter(
            and_(
                func.year(SettlementData.date) == year,
                func.month(SettlementData.date) == month
            )
        ).all()
        
        logger.info(f"Found {len(banking_records)} banking records and {len(settlement_records)} settlement records")
        
        validation_results = {
            'month': date_str,
            'banking_records_count': len(banking_records),
            'settlement_records_count': len(settlement_records),
            'validations': [],
            'errors': [],
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0
            }
        }
        
        # Test each banking record against source data
        for banking_record in banking_records:
            test_result = self._validate_single_record(banking_record, settlement_records, year, month)
            validation_results['validations'].append(test_result)
            validation_results['summary']['total_tests'] += 1
            
            if test_result['status'] == 'PASSED':
                validation_results['summary']['passed_tests'] += 1
            else:
                validation_results['summary']['failed_tests'] += 1
                validation_results['errors'].append(test_result)
        
        return validation_results
    
    def _validate_single_record(self, banking_record, settlement_records, year, month):
        """Validate a single banking record against source settlement data"""
        
        # Filter settlement records that should contribute to this banking record
        matching_settlement_records = []
        
        for settlement_record in settlement_records:
            # Get plant name for settlement record
            plant_name = self._get_plant_name(settlement_record.plant_id)
            slot_name = self.get_slot_name_from_time(settlement_record.time)
            
            # Check if this settlement record matches the banking record criteria
            if (settlement_record.client_name == banking_record.client_name and
                plant_name == banking_record.plant_name and
                settlement_record.cons_unit == banking_record.cons_unit and
                settlement_record.type == banking_record.type and
                slot_name == banking_record.slot_name):
                matching_settlement_records.append(settlement_record)
        
        # Calculate expected values from matching records
        expected_surplus_demand = sum(
            record.surplus_demand or Decimal('0.00') 
            for record in matching_settlement_records
        )
        expected_surplus_generation = sum(
            record.surplus_generation or Decimal('0.00') 
            for record in matching_settlement_records
        )
        expected_matched_settled = sum(
            record.settled or Decimal('0.00') 
            for record in matching_settlement_records
        )
        
        # Compare with banking record values
        surplus_demand_correct = banking_record.surplus_demand_sum == expected_surplus_demand
        surplus_generation_correct = banking_record.surplus_generation_sum == expected_surplus_generation
        matched_settled_correct = banking_record.matched_settled_sum == expected_matched_settled
        
        test_result = {
            'record_key': f"{banking_record.client_name}-{banking_record.plant_name}-{banking_record.cons_unit}-{banking_record.slot_name}-{banking_record.type}",
            'matching_settlement_count': len(matching_settlement_records),
            'tests': {
                'surplus_demand': {
                    'expected': expected_surplus_demand,
                    'actual': banking_record.surplus_demand_sum,
                    'passed': surplus_demand_correct
                },
                'surplus_generation': {
                    'expected': expected_surplus_generation,
                    'actual': banking_record.surplus_generation_sum,
                    'passed': surplus_generation_correct
                },
                'matched_settled': {
                    'expected': expected_matched_settled,
                    'actual': banking_record.matched_settled_sum,
                    'passed': matched_settled_correct
                }
            },
            'status': 'PASSED' if all([surplus_demand_correct, surplus_generation_correct, matched_settled_correct]) else 'FAILED'
        }
        
        return test_result
    
    def _get_plant_name(self, plant_id: str) -> str:
        """Get plant name from plant_id - same logic as main code"""
        from models import TblPlants
        plant = self.session.query(TblPlants).filter(TblPlants.plant_id == plant_id).first()
        return plant.plant_name if plant else plant_id
    
    def print_validation_report(self, validation_results):
        """Print detailed validation report"""
        print(f"\n{'='*80}")
        print(f"DATA VALIDATION REPORT FOR {validation_results['month']}")
        print(f"{'='*80}")
        
        print(f"Banking Records: {validation_results['banking_records_count']}")
        print(f"Settlement Records: {validation_results['settlement_records_count']}")
        print(f"Total Tests: {validation_results['summary']['total_tests']}")
        print(f"Passed Tests: {validation_results['summary']['passed_tests']}")
        print(f"Failed Tests: {validation_results['summary']['failed_tests']}")
        
        success_rate = (validation_results['summary']['passed_tests'] / 
                       validation_results['summary']['total_tests'] * 100) if validation_results['summary']['total_tests'] > 0 else 0
        print(f"Success Rate: {success_rate:.1f}%")
        
        if validation_results['errors']:
            print(f"\nFAILED VALIDATIONS:")
            for error in validation_results['errors']:
                print(f"\n  Record: {error['record_key']}")
                print(f"  Matching Settlement Records: {error['matching_settlement_count']}")
                
                for test_name, test_data in error['tests'].items():
                    if not test_data['passed']:
                        print(f"    FAILED {test_name}:")
                        print(f"       Expected: {test_data['expected']}")
                        print(f"       Actual: {test_data['actual']}")
                        print(f"       Difference: {test_data['actual'] - test_data['expected']}")
        else:
            print(f"\nALL VALIDATIONS PASSED!")
        
        return success_rate == 100.0

def main():
    """Run data validation tests"""
    DATABASE_URL = "mysql+pymysql://root:test123@localhost/energy_db"
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        validator = DataValidator(session)
        
        # Get all available months
        available_months = session.query(
            func.year(SettlementData.date).label('year'),
            func.month(SettlementData.date).label('month')
        ).distinct().order_by('year', 'month').all()
        
        print(f"Found {len(available_months)} months to validate")
        
        all_passed = True
        total_months = 0
        passed_months = 0
        
        for year, month in available_months:
            validation_results = validator.validate_aggregated_values(year, month)
            
            if 'status' in validation_results and validation_results['status'] == 'NO_DATA':
                print(f"\nWARNING: {validation_results['message']}")
                continue
            
            total_months += 1
            month_passed = validator.print_validation_report(validation_results)
            
            if month_passed:
                passed_months += 1
            else:
                all_passed = False
        
        # Final summary
        print(f"\n{'='*80}")
        print(f"FINAL VALIDATION SUMMARY")
        print(f"{'='*80}")
        print(f"Total Months Tested: {total_months}")
        print(f"Months Passed: {passed_months}")
        print(f"Months Failed: {total_months - passed_months}")
        print(f"Overall Success Rate: {(passed_months/total_months*100):.1f}%" if total_months > 0 else "N/A")
        
        if all_passed:
            print(f"\nSUCCESS: ALL DATA VALIDATION TESTS PASSED!")
            print(f"SUCCESS: The three initial aggregated columns contain correct data!")
        else:
            print(f"\nWARNING: Some validation tests failed - data integrity issues detected!")
    
    except Exception as e:
        logger.error(f"Validation error: {str(e)}")
        session.rollback()
    
    finally:
        session.close()

if __name__ == "__main__":
    main()