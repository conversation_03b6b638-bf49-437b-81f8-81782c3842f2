import pandas as pd
import numpy as np
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_average_pr():
    """
    Calculate the average PR for each datetime and add it as a new column
    """
    try:
        # Read the CSV file
        input_file = "scada_combined_data_with_pr.csv"
        output_file = "scada_combined_data_with_avg_pr.csv"
        
        logger.info(f"Reading CSV file: {input_file}")
        df = pd.read_csv(input_file)
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Define PR columns (INV_1_PR to INV_16_PR)
        pr_columns = [f'INV_{i}_PR' for i in range(1, 17)]
        logger.info(f"PR columns: {pr_columns}")
        
        # Check if all PR columns exist in the dataframe
        missing_columns = [col for col in pr_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"Missing PR columns: {missing_columns}")
        
        existing_pr_columns = [col for col in pr_columns if col in df.columns]
        logger.info(f"Found {len(existing_pr_columns)} PR columns")
        
        # Calculate average PR for each row
        # Convert empty strings to NaN first, then to numeric
        pr_data = df[existing_pr_columns]
        
        # Replace empty strings with NaN
        pr_data = pr_data.replace('', np.nan)
        
        # Convert to numeric, errors='coerce' will convert non-numeric values to NaN
        pr_data = pr_data.apply(pd.to_numeric, errors='coerce')
        
        # Calculate average PR for each row (ignoring NaN values)
        df['Average_PR'] = pr_data.mean(axis=1, skipna=True)
        
        # Round the average PR to 2 decimal places
        df['Average_PR'] = df['Average_PR'].round(2)
        
        # Count how many valid PR values were used for each average
        df['PR_Count'] = pr_data.count(axis=1)
        
        # Save the result to a new CSV file
        df.to_csv(output_file, index=False)
        logger.info(f"Results saved to: {output_file}")
        
        # Display some statistics
        logger.info(f"Average PR statistics:")
        logger.info(f"  - Mean: {df['Average_PR'].mean():.2f}")
        logger.info(f"  - Min: {df['Average_PR'].min():.2f}")
        logger.info(f"  - Max: {df['Average_PR'].max():.2f}")
        logger.info(f"  - Rows with valid Average PR: {df['Average_PR'].notna().sum()}")
        logger.info(f"  - Rows with missing Average PR: {df['Average_PR'].isna().sum()}")
        
        # Show first few rows with the new Average_PR column
        logger.info("\nFirst 10 rows with Average PR:")
        display_columns = ['Date & Time', 'Average_PR', 'PR_Count']
        print(df[display_columns].head(10).to_string(index=False))
        
        # Show distribution of PR_Count (how many inverters contributed to each average)
        logger.info(f"\nDistribution of PR_Count (number of inverters with valid PR):")
        pr_count_distribution = df['PR_Count'].value_counts().sort_index()
        for count, freq in pr_count_distribution.items():
            logger.info(f"  - {count} inverters: {freq} records")
        
        return df
        
    except Exception as e:
        logger.error(f"Error in calculate_average_pr: {e}")
        raise

def verify_calculations():
    """
    Verify some calculations manually to ensure correctness
    """
    try:
        logger.info("\nVerifying calculations...")
        
        # Read the original file
        df = pd.read_csv("scada_combined_data_with_pr.csv")
        
        # Check a few specific rows manually
        pr_columns = [f'INV_{i}_PR' for i in range(1, 17)]
        existing_pr_columns = [col for col in pr_columns if col in df.columns]
        
        # Verify first few rows
        for i in range(min(3, len(df))):
            row_data = df.iloc[i]
            datetime_val = row_data['Date & Time']
            
            # Get PR values for this row
            pr_values = []
            for col in existing_pr_columns:
                val = row_data[col]
                if pd.notna(val) and val != '':
                    try:
                        pr_values.append(float(val))
                    except:
                        pass
            
            if pr_values:
                manual_avg = sum(pr_values) / len(pr_values)
                logger.info(f"Row {i+1} ({datetime_val}):")
                logger.info(f"  - Valid PR values: {pr_values}")
                logger.info(f"  - Manual average: {manual_avg:.2f}")
                logger.info(f"  - Count: {len(pr_values)}")
            else:
                logger.info(f"Row {i+1} ({datetime_val}): No valid PR values")
                
    except Exception as e:
        logger.error(f"Error in verify_calculations: {e}")

if __name__ == "__main__":
    logger.info("Starting Average PR calculation...")
    
    # Calculate average PR
    result_df = calculate_average_pr()
    
    # Verify some calculations
    verify_calculations()
    
    logger.info("Process completed successfully!")