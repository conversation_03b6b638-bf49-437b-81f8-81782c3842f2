from sqlalchemy import create_engine, func, and_, or_
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta, time
from decimal import Decimal
import logging
from typing import Dict, List, Tuple
from models import SettlementData, BankingSettlement, Base

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Karnataka 2025 Policy charges
INTRA_TOD_CHARGES = Decimal('0.02')  # 2%
INTER_TOD_CHARGES = Decimal('0.08')  # 8%

# Define time slots according to Karnataka 2025 policy
TIME_SLOTS = {
    'Morning Peak': {
        'start_time': time(6, 0),   # 6:00 AM
        'end_time': time(9, 0),     # 9:00 AM
        'slot_time': '6am to 9am'
    },
    'Day (Normal)': {
        'start_time': time(9, 0),   # 9:00 AM
        'end_time': time(18, 0),    # 6:00 PM
        'slot_time': '9am to 6pm'
    },
    'Evening Peak': {
        'start_time': time(18, 0),  # 6:00 PM
        'end_time': time(22, 0),    # 10:00 PM
        'slot_time': '6pm to 10pm'
    },
    'Night Off-Peak': {
        'start_time': time(22, 0),  # 10:00 PM
        'end_time': time(6, 0),     # 6:00 AM (next day)
        'slot_time': '10pm to 6am'
    }
}

# Define relationships for inter-TOD settlement
SLOT_RELATIONSHIPS = {
    "Morning Peak": ["Evening Peak", "Day (Normal)", "Night Off-Peak"],
    "Evening Peak": ["Morning Peak", "Day (Normal)", "Night Off-Peak"],
    "Day (Normal)": ["Night Off-Peak", "Evening Peak", "Morning Peak"],
    "Night Off-Peak": ["Morning Peak", "Day (Normal)", "Evening Peak"]
}

class MonthlyBankingProcessor:
    def __init__(self, db_session):
        self.session = db_session
    
    def process_monthly_banking(self, year: int, month: int) -> bool:
        """
        Main method to process monthly banking settlement following Karnataka 2025 policy
        
        Args:
            year: Year for settlement (e.g., 2024)
            month: Month for settlement (1-12)
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            date_str = f"{year:04d}-{month:02d}"
            logger.info(f"Starting Karnataka 2025 monthly banking settlement for {date_str}")
            
            # Step 1: Clear existing banking data for this month
            self._clear_existing_banking_data(date_str)
            
            # Step 2: Aggregate settlement data by slot
            aggregated_data = self._aggregate_settlement_data(year, month)
            
            # Step 3: Create initial banking records
            self._create_initial_banking_records(aggregated_data, date_str)
            
            # Step 4: Process intra-TOD settlement (within same slot, 2% charges)
            self._process_intra_tod_settlement(date_str)
            
            # Step 5: Process inter-TOD settlement (across different slots, 8% charges)
            self._process_inter_tod_settlement(date_str)
            
            # Step 6: Initialize any remaining inter-settlement values
            self._initialize_inter_settlement_values(date_str)
            
            self.session.commit()
            logger.info(f"Karnataka 2025 monthly banking settlement completed for {date_str}")
            return True
            
        except Exception as e:
            logger.error(f"Error in monthly banking settlement: {str(e)}")
            self.session.rollback()
            return False
    
    def _clear_existing_banking_data(self, date_str: str):
        """Clear existing banking settlement data for the given month"""
        try:
            deleted_count = self.session.query(BankingSettlement).filter(
                BankingSettlement.date == date_str
            ).delete(synchronize_session=False)
            self.session.flush()  # Flush the delete operation
            logger.info(f"Cleared {deleted_count} existing banking records for {date_str}")
        except Exception as e:
            logger.error(f"Error clearing existing data: {str(e)}")
            raise
    
    def _get_slot_name_from_time(self, time_obj: time) -> Tuple[str, str]:
        """
        Determine slot name and slot_time string from a time object
        
        Args:
            time_obj: Time object to categorize
            
        Returns:
            Tuple of (slot_name, slot_time)
        """
        for slot_name, slot_info in TIME_SLOTS.items():
            start_time = slot_info['start_time']
            end_time = slot_info['end_time']
            
            # Handle overnight slot (Night: 22:00 to 06:00)
            if start_time > end_time:  # Crosses midnight
                if time_obj >= start_time or time_obj < end_time:
                    return slot_name, slot_info['slot_time']
            else:  # Normal slot within same day
                if start_time <= time_obj < end_time:
                    return slot_name, slot_info['slot_time']
        
        # Default fallback (shouldn't happen with proper time slots)
        return 'Unknown', '00:00-00:00'

    def _aggregate_settlement_data(self, year: int, month: int) -> List[Dict]:
        """
        Aggregate settlement data by client, plant, cons_unit, type, and slot
        
        Returns:
            List of dictionaries containing aggregated data
        """
        logger.info(f"Aggregating settlement data for {year}-{month:02d}")
        
        # First, get all settlement data for the month
        settlement_records = self.session.query(SettlementData).filter(
            and_(
                func.year(SettlementData.date) == year,
                func.month(SettlementData.date) == month
            )
        ).all()
        
        logger.info(f"Found {len(settlement_records)} settlement records")
        
        # Group data by key and determine time slots
        grouped_data = {}
        
        for record in settlement_records:
            # Determine slot from time
            slot_name, slot_time = self._get_slot_name_from_time(record.time)
            
            # Get plant name for this record
            plant_name = self._get_plant_name(record.plant_id)
            
            # Create grouping key based on unique constraint columns
            # The unique constraint is: client_name, plant_name, cons_unit, slot_name, date
            key = (
                record.client_name,
                plant_name,  # Use plant_name instead of plant_id
                record.cons_unit,
                slot_name,
                record.type  # Include type for better grouping
            )
            
            if key not in grouped_data:
                grouped_data[key] = {
                    'client_name': record.client_name,
                    'plant_id': record.plant_id,  # Keep plant_id for reference
                    'plant_name': plant_name,
                    'cons_unit': record.cons_unit,
                    'type': record.type,
                    'slot_name': slot_name,
                    'slot_time': slot_time,
                    'surplus_demand_sum': Decimal('0.00'),
                    'surplus_generation_sum': Decimal('0.00'),
                    'matched_settled_sum': Decimal('0.00')
                }
            
            # Aggregate values
            grouped_data[key]['surplus_demand_sum'] += record.surplus_demand or Decimal('0.00')
            grouped_data[key]['surplus_generation_sum'] += record.surplus_generation or Decimal('0.00')
            grouped_data[key]['matched_settled_sum'] += record.settled or Decimal('0.00')
        
        # Convert to list
        aggregated_data = list(grouped_data.values())
        
        logger.info(f"Created {len(aggregated_data)} aggregated settlement records")
        
        # Log sample data for debugging
        if aggregated_data:
            sample = aggregated_data[0]
            logger.info(f"Sample record: {sample['client_name']}, {sample['plant_name']}, {sample['cons_unit']}, {sample['slot_name']}")
        
        return aggregated_data
    
    def _get_plant_name(self, plant_id: str) -> str:
        """Get plant name from plant_id"""
        # This should query your TblPlants table
        # For now, returning plant_id as plant_name
        from models import TblPlants
        plant = self.session.query(TblPlants).filter(TblPlants.plant_id == plant_id).first()
        return plant.plant_name if plant else plant_id
    
    def _create_initial_banking_records(self, aggregated_data: List[Dict], date_str: str):
        """Create initial banking settlement records"""
        logger.info(f"Creating {len(aggregated_data)} initial banking records")
        
        # Check for potential duplicates before insertion
        unique_keys = set()
        filtered_data = []
        
        for data in aggregated_data:
            # Create key based on unique constraint
            unique_key = (
                data['client_name'],
                data['plant_name'],
                data['cons_unit'],
                data['slot_name'],
                date_str
            )
            
            if unique_key in unique_keys:
                logger.warning(f"Duplicate key found: {unique_key}")
                # Merge with existing record
                existing_record = next(
                    (record for record in filtered_data 
                     if (record['client_name'], record['plant_name'], record['cons_unit'], record['slot_name']) == unique_key[:-1]),
                    None
                )
                if existing_record:
                    existing_record['surplus_demand_sum'] += data['surplus_demand_sum']
                    existing_record['surplus_generation_sum'] += data['surplus_generation_sum']
                    existing_record['matched_settled_sum'] += data['matched_settled_sum']
                    logger.info(f"Merged duplicate record for {unique_key}")
            else:
                unique_keys.add(unique_key)
                filtered_data.append(data)
        
        logger.info(f"After deduplication: {len(filtered_data)} records to insert")
        
        banking_records = []
        for data in filtered_data:
            banking_record = BankingSettlement(
                client_name=data['client_name'],
                plant_name=data['plant_name'],
                date=date_str,
                type=data['type'],
                cons_unit=data['cons_unit'],
                slot_name=data['slot_name'],
                slot_time=data['slot_time'],
                surplus_demand_sum=data['surplus_demand_sum'],
                surplus_generation_sum=data['surplus_generation_sum'],
                matched_settled_sum=data['matched_settled_sum'],
                # Initialize after-settlement values with original values
                surplus_generation_sum_after_intra=data['surplus_generation_sum'],
                surplus_demand_sum_after_intra=data['surplus_demand_sum'],
                intra_settlement=Decimal('0.00'),
                surplus_generation_sum_after_inter=data['surplus_generation_sum'],
                surplus_demand_sum_after_inter=data['surplus_demand_sum'],
                inter_settlement=Decimal('0.00')
            )
            banking_records.append(banking_record)
        
        try:
            self.session.add_all(banking_records)
            self.session.flush()  # Flush to get IDs and catch any constraint violations
            logger.info("Initial banking records created successfully")
        except Exception as e:
            logger.error(f"Error creating banking records: {str(e)}")
            raise
    
    def _process_intra_tod_settlement(self, date_str: str):
        """
        Process intra-TOD settlement: Match surplus generation and demand within same slot
        Karnataka 2025 policy - 2% charges
        """
        logger.info(f"Processing intra-TOD settlement with charges: {INTRA_TOD_CHARGES*100}%")
        
        # Get all unique slots for this month
        slots = self.session.query(BankingSettlement.slot_name).filter(
            BankingSettlement.date == date_str
        ).distinct().all()
        
        total_intra_settled = Decimal('0.00')
        
        for slot_tuple in slots:
            slot_name = slot_tuple[0]
            slot_settled = self._process_slot_intra_tod_settlement(date_str, slot_name)
            total_intra_settled += slot_settled
        
        logger.info(f"Total intra-TOD settlement: {total_intra_settled}")
    
    def _process_slot_intra_tod_settlement(self, date_str: str, slot_name: str) -> Decimal:
        """Process intra-TOD settlement for a specific slot following Karnataka 2025 logic"""
        
        # Get all records for this slot
        records = self.session.query(BankingSettlement).filter(
            and_(
                BankingSettlement.date == date_str,
                BankingSettlement.slot_name == slot_name
            )
        ).all()
        
        total_slot_settled = Decimal('0.00')
        
        # Process each record using Karnataka 2025 intra-TOD settlement logic
        for record in records:
            demand = record.surplus_demand_sum
            lapsed = record.surplus_generation_sum
            
            # Apply Karnataka 2025 intra-TOD settlement logic
            demand_after, lapsed_after, settlement_amount = self._intra_tod_settlement_karnataka(
                demand, lapsed, INTRA_TOD_CHARGES
            )
            
            # Update the record with settlement results
            record.surplus_demand_sum_after_intra = demand_after
            record.surplus_generation_sum_after_intra = lapsed_after
            record.intra_settlement = settlement_amount
            
            total_slot_settled += settlement_amount
            
            logger.debug(f"Intra-TOD Settlement - Slot: {slot_name}, Client: {record.client_name}, "
                        f"Original Demand: {demand}, Original Lapsed: {lapsed}, "
                        f"After Demand: {demand_after}, After Lapsed: {lapsed_after}, "
                        f"Settlement: {settlement_amount}")
        
        return total_slot_settled
    
    def _intra_tod_settlement_karnataka(self, surplus_demand_sum: Decimal, surplus_generation_sum: Decimal, intra_tod_charges: Decimal) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Implement 3-stage settlement logic following user's specification:
        
        Special Case Handling:
        - Generation=0, Consumption>0: Skip matched, skip intra, pass demand unchanged to inter
        - Generation>0, Consumption=0: Skip matched, skip intra, pass generation unchanged
        - Both>0: Apply normal matched + intra logic
        
        Args:
            surplus_demand_sum: Original surplus demand 
            surplus_generation_sum: Original surplus generation
            intra_tod_charges: TOD charges as Decimal (e.g., Decimal('0.02') for 2%)
            
        Returns:
            Tuple of (demand_after_intra, generation_after_intra, intra_settlement_amount)
        """
        
        # Special Case 1: Generation=0, Consumption>0
        if surplus_generation_sum == Decimal('0.00') and surplus_demand_sum > Decimal('0.00'):
            # 1️⃣ Skip matched settlement → matched=0
            # 2️⃣ Skip intra settlement → intra=0 (needs generation)  
            # 3️⃣ Pass demand unchanged to inter stage
            return surplus_demand_sum, Decimal('0.00'), Decimal('0.00')
        
        # Special Case 2: Generation>0, Consumption=0  
        elif surplus_generation_sum > Decimal('0.00') and surplus_demand_sum == Decimal('0.00'):
            # 1️⃣ Skip matched settlement → matched=0
            # 2️⃣ Skip intra settlement → intra=0 (no demand)
            # 3️⃣ Pass generation unchanged
            return Decimal('0.00'), surplus_generation_sum, Decimal('0.00')
        
        # Normal Case: Both demand and generation exist
        elif surplus_generation_sum > Decimal('0.00') and surplus_demand_sum > Decimal('0.00'):
            # 1️⃣ MATCHED STAGE: Already handled in aggregation
            # Input demand and generation are already "remaining after matching"
            remaining_surplus_demand = surplus_demand_sum 
            remaining_surplus_generation = surplus_generation_sum 
            
            # 2️⃣ INTRA STAGE: Apply intra settlement with 2% charges
            required_generation_with_charges = remaining_surplus_demand * (Decimal('1') + intra_tod_charges)
            
            if remaining_surplus_generation >= required_generation_with_charges:
                # Full intra settlement - all demand can be settled
                demand_after_intra = Decimal('0.00')
                generation_after_intra = remaining_surplus_generation - required_generation_with_charges
                intra_settlement_amount = required_generation_with_charges
                
            else:
                # Partial intra settlement - limited by available generation
                settled_demand = remaining_surplus_generation / (Decimal('1') + intra_tod_charges)
                demand_after_intra = remaining_surplus_demand - settled_demand
                generation_after_intra = Decimal('0.00')
                intra_settlement_amount = remaining_surplus_generation
                
            return demand_after_intra, generation_after_intra, intra_settlement_amount
        
        else:
            # Both are zero - nothing to settle
            return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')
    

    
    def _process_inter_tod_settlement(self, date_str: str):
        """
        Process inter-TOD settlement: Apply financial settlement to clear remaining demand
        Logic: Settle all remaining demand with 8% penalty (standard financial settlement)
        - Inter settlement fully clears remaining surplus demand
        - Customer pays 8% extra cost
        - Final demand becomes 0 after settlement
        """
        logger.info(f"Processing inter-TOD settlement with charges: {INTER_TOD_CHARGES*100}%")
        
        # Get all records for this month
        records = self.session.query(BankingSettlement).filter(
            BankingSettlement.date == date_str
        ).all()
        
        total_inter_settled = Decimal('0.00')
        
        # Process each record using simplified inter-TOD settlement logic
        for record in records:
            demand_after_intra = record.surplus_demand_sum_after_intra
            generation_after_intra = record.surplus_generation_sum_after_intra
            
            # Apply inter-TOD settlement logic
            demand_after_inter, generation_after_inter, inter_settlement_amount = self._inter_tod_settlement_logic(
                demand_after_intra, generation_after_intra, INTER_TOD_CHARGES
            )
            
            # Update the record with settlement results
            record.surplus_demand_sum_after_inter = demand_after_inter
            record.surplus_generation_sum_after_inter = generation_after_inter
            record.inter_settlement = inter_settlement_amount
            
            total_inter_settled += inter_settlement_amount
            
            if inter_settlement_amount > Decimal('0.00'):
                logger.debug(f"Inter-TOD Settlement - Client: {record.client_name}, Slot: {record.slot_name}, "
                            f"Demand after intra: {demand_after_intra}, Generation after intra: {generation_after_intra}, "
                            f"Final Demand: {demand_after_inter}, Final Generation: {generation_after_inter}, "
                            f"Inter Settlement: {inter_settlement_amount}")
        
        logger.info(f"Total inter-TOD settlement: {total_inter_settled}")
        logger.info("Inter-TOD settlement completed")
    
    def _inter_tod_settlement_logic(self, demand_after_intra: Decimal, generation_after_intra: Decimal, inter_tod_charges: Decimal) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Implement inter-TOD settlement logic as specified:
        
        For generation=0, consumption>0 case:
        3️⃣ Compute inter settlement:
        - remaining_demand = surplus_demand_sum (passed as demand_after_intra)
        - total_inter_settlement = remaining_demand / (1 + INTER_TOD_CHARGES)
        - surplus_demand_after_inter = remaining_demand - total_inter_settlement
        4️⃣ Set surplus_generation_after_* = 0 → because there's no generation
        
        Args:
            demand_after_intra: Remaining demand after intra settlement
            generation_after_intra: Remaining generation after intra settlement  
            inter_tod_charges: Inter-TOD charges as Decimal (e.g., Decimal('0.08') for 8%)
            
        Returns:
            Tuple of (demand_after_inter, generation_after_inter, inter_settlement_amount)
        """
        
        # 4️⃣ Generation doesn't change in inter settlement (financial settlement only)
        generation_after_inter = generation_after_intra
        
        # 3️⃣ Apply inter settlement logic for remaining demand
        if demand_after_intra > Decimal('0.00'):
            # Calculate inter settlement amount using the specified formula
            remaining_demand = demand_after_intra
            total_inter_settlement = remaining_demand / (Decimal('1') + inter_tod_charges)
            
            # Calculate remaining demand after inter settlement
            surplus_demand_after_inter = remaining_demand - total_inter_settlement
            
            return surplus_demand_after_inter, generation_after_inter, total_inter_settlement
        else:
            # No remaining demand to settle
            return Decimal('0.00'), generation_after_inter, Decimal('0.00')
    
    # Old relationship-based functions removed - now using simplified individual record approach
    
    # Old slot-based settlement functions removed
    
    def _initialize_inter_settlement_values(self, date_str: str):
        """Initialize inter settlement values for records that weren't processed - now simplified"""
        # With the new approach, all records are processed individually
        # This function is kept for compatibility but may not be needed
        logger.info("Inter settlement values initialization - all records processed individually in new approach")
    

    
    def generate_slot_based_report(self, year: int, month: int) -> Dict:
        """Generate detailed slot-based banking report"""
        date_str = f"{year:04d}-{month:02d}"
        
        # Query data grouped by slots
        slot_summary = {}
        
        for slot_name, slot_info in TIME_SLOTS.items():
            slot_data = self.session.query(
                func.sum(BankingSettlement.surplus_demand_sum).label('total_demand'),
                func.sum(BankingSettlement.surplus_generation_sum).label('total_generation'),
                func.sum(BankingSettlement.matched_settled_sum).label('total_matched'),
                func.sum(BankingSettlement.intra_settlement).label('total_intra'),
                func.sum(BankingSettlement.inter_settlement).label('total_inter'),
                func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('remaining_generation'),
                func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('remaining_demand'),
                func.count().label('record_count')
            ).filter(
                and_(
                    BankingSettlement.date == date_str,
                    BankingSettlement.slot_name == slot_name
                )
            ).first()
            
            slot_summary[slot_name] = {
                'slot_time': slot_info['slot_time'],
                'total_surplus_demand': slot_data.total_demand or Decimal('0.00'),
                'total_surplus_generation': slot_data.total_generation or Decimal('0.00'),
                'total_matched_settled': slot_data.total_matched or Decimal('0.00'),
                'total_intra_settlement': slot_data.total_intra or Decimal('0.00'),
                'total_inter_settlement': slot_data.total_inter or Decimal('0.00'),
                'remaining_surplus_generation': slot_data.remaining_generation or Decimal('0.00'),
                'remaining_surplus_demand': slot_data.remaining_demand or Decimal('0.00'),
                'record_count': slot_data.record_count or 0,
                'settlement_efficiency': self._calculate_settlement_efficiency(
                    slot_data.total_generation or Decimal('0.00'),
                    slot_data.remaining_generation or Decimal('0.00')
                )
            }
        
        return {
            'date': date_str,
            'slot_summary': slot_summary,
            'time_slots_definition': TIME_SLOTS
        }
    
    def generate_karnataka_summary(self, year: int, month: int) -> Dict:
        """Generate Karnataka 2025 policy summary similar to test file output"""
        date_str = f"{year:04d}-{month:02d}"
        
        # Get summary by slot
        slot_summary = {}
        for slot_name in TIME_SLOTS.keys():
            slot_data = self.session.query(
                func.sum(BankingSettlement.surplus_demand_sum).label('original_demand'),
                func.sum(BankingSettlement.surplus_generation_sum).label('original_generation'),
                func.sum(BankingSettlement.surplus_demand_sum_after_intra).label('demand_after_intra'),
                func.sum(BankingSettlement.surplus_generation_sum_after_intra).label('generation_after_intra'),
                func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('demand_after_inter'),
                func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('generation_after_inter'),
                func.sum(BankingSettlement.intra_settlement).label('intra_settlement'),
                func.sum(BankingSettlement.inter_settlement).label('inter_settlement')
            ).filter(
                and_(
                    BankingSettlement.date == date_str,
                    BankingSettlement.slot_name == slot_name
                )
            ).first()
            
            slot_summary[slot_name] = {
                'original_surplus_demand': slot_data.original_demand or Decimal('0.00'),
                'original_surplus_generation': slot_data.original_generation or Decimal('0.00'),
                'demand_after_intra': slot_data.demand_after_intra or Decimal('0.00'),
                'generation_after_intra': slot_data.generation_after_intra or Decimal('0.00'),
                'final_grid_consumption': slot_data.demand_after_inter or Decimal('0.00'),
                'final_surplus_generation': slot_data.generation_after_inter or Decimal('0.00'),
                'intra_settlement': slot_data.intra_settlement or Decimal('0.00'),
                'inter_settlement': slot_data.inter_settlement or Decimal('0.00')
            }
        
        # Calculate overall summary
        overall_summary = self.session.query(
            func.sum(BankingSettlement.surplus_demand_sum).label('total_demand'),
            func.sum(BankingSettlement.surplus_generation_sum).label('total_generation'),
            func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('grid_consumption'),
            func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('surplus_generation'),
            func.sum(BankingSettlement.intra_settlement).label('total_intra'),
            func.sum(BankingSettlement.inter_settlement).label('total_inter')
        ).filter(
            BankingSettlement.date == date_str
        ).first()
        
        total_consumption = overall_summary.total_demand or Decimal('0.00')
        grid_consumption = overall_summary.grid_consumption or Decimal('0.00')
        replacement = total_consumption - grid_consumption
        
        return {
            'month': month,
            'year': year,
            'slot_summary': slot_summary,
            'overall_summary': {
                'total_consumption': total_consumption,
                'grid_consumption': grid_consumption,
                'replacement': replacement,
                'surplus_generation': overall_summary.surplus_generation or Decimal('0.00'),
                'total_intra_settlement': overall_summary.total_intra or Decimal('0.00'),
                'total_inter_settlement': overall_summary.total_inter or Decimal('0.00')
            },
            'policy': 'Karnataka_2025',
            'intra_tod_charges': float(INTRA_TOD_CHARGES * 100),
            'inter_tod_charges': float(INTER_TOD_CHARGES * 100),
            'slot_relationships': SLOT_RELATIONSHIPS
        }
    
    def _calculate_settlement_efficiency(self, total_generation: Decimal, remaining_generation: Decimal) -> float:
        """Calculate settlement efficiency percentage"""
        if total_generation <= 0:
            return 0.0
        
        settled_generation = total_generation - remaining_generation
        efficiency = float(settled_generation / total_generation * 100)
        return round(efficiency, 2)
    def get_available_months(self) -> List[Tuple[int, int]]:
        """Get all available year-month combinations from settlement data"""
        unique_months = self.session.query(
            func.year(SettlementData.date).label('year'),
            func.month(SettlementData.date).label('month')
        ).distinct().order_by('year', 'month').all()
        
        return [(year, month) for year, month in unique_months]
    
    def process_all_available_months(self) -> Dict:
        """Process banking settlement for all available months"""
        available_months = self.get_available_months()
        
        results = {
            'total_months': len(available_months),
            'processed_successfully': 0,
            'failed_months': [],
            'processing_summary': []
        }
        
        logger.info(f"Starting processing for {len(available_months)} available months")
        
        for year, month in available_months:
            try:
                logger.info(f"Processing {year}-{month:02d}...")
                success = self.process_monthly_banking(year, month)
                
                if success:
                    results['processed_successfully'] += 1
                    results['processing_summary'].append({
                        'year': year,
                        'month': month,
                        'status': 'SUCCESS',
                        'summary': self.get_banking_summary(year, month)
                    })
                    logger.info(f"SUCCESS: {year}-{month:02d} processed successfully")
                else:
                    results['failed_months'].append((year, month))
                    results['processing_summary'].append({
                        'year': year,
                        'month': month,
                        'status': 'FAILED',
                        'error': 'Processing returned False'
                    })
                    logger.error(f"FAILED: {year}-{month:02d} processing failed")
                    
            except Exception as e:
                results['failed_months'].append((year, month))
                results['processing_summary'].append({
                    'year': year,
                    'month': month,
                    'status': 'ERROR',
                    'error': str(e)
                })
                logger.error(f"ERROR: {year}-{month:02d} processing error: {str(e)}")
        
        results['success_rate'] = (results['processed_successfully'] / results['total_months'] * 100) if results['total_months'] > 0 else 0
        
        return results
    
    def process_and_show_karnataka_summary(self, year: int, month: int) -> Dict:
        """Process banking settlement and return Karnataka 2025 summary"""
        # Process the banking settlement
        success = self.process_monthly_banking(year, month)
        
        if success:
            # Generate Karnataka summary
            summary = self.generate_karnataka_summary(year, month)
            
            # Log key metrics
            logger.info(f"Karnataka 2025 Summary for {year}-{month:02d}:")
            logger.info(f"  Total Consumption: {summary['overall_summary']['total_consumption']}")
            logger.info(f"  Grid Consumption: {summary['overall_summary']['grid_consumption']}")
            logger.info(f"  Replacement: {summary['overall_summary']['replacement']}")
            logger.info(f"  Intra-TOD Settlement: {summary['overall_summary']['total_intra_settlement']}")
            logger.info(f"  Inter-TOD Settlement: {summary['overall_summary']['total_inter_settlement']}")
            
            return summary
        else:
            raise Exception(f"Failed to process banking settlement for {year}-{month:02d}")

    def get_banking_summary(self, year: int, month: int) -> Dict:
        """Get summary of banking settlement for a given month"""
        date_str = f"{year:04d}-{month:02d}"
        
        summary = self.session.query(
            func.sum(BankingSettlement.surplus_demand_sum).label('total_demand'),
            func.sum(BankingSettlement.surplus_generation_sum).label('total_generation'),
            func.sum(BankingSettlement.matched_settled_sum).label('total_matched'),
            func.sum(BankingSettlement.intra_settlement).label('total_intra'),
            func.sum(BankingSettlement.inter_settlement).label('total_inter'),
            func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('remaining_generation'),
            func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('remaining_demand')
        ).filter(
            BankingSettlement.date == date_str
        ).first()
        
        return {
            'date': date_str,
            'total_surplus_demand': summary.total_demand or Decimal('0.00'),
            'total_surplus_generation': summary.total_generation or Decimal('0.00'),
            'total_matched_settled': summary.total_matched or Decimal('0.00'),
            'total_intra_settlement': summary.total_intra or Decimal('0.00'),
            'total_inter_settlement': summary.total_inter or Decimal('0.00'),
            'remaining_surplus_generation': summary.remaining_generation or Decimal('0.00'),
            'remaining_surplus_demand': summary.remaining_demand or Decimal('0.00')
        }


# Example usage and testing
def main():
    """Process banking settlement for all available data, month by month"""
    
    # Database connection setup
    DATABASE_URL = "mysql+pymysql://root:test123@localhost/energy_db"
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create banking processor
        processor = MonthlyBankingProcessor(session)
        
        # Get all unique year-month combinations from settlement data
        unique_months = session.query(
            func.year(SettlementData.date).label('year'),
            func.month(SettlementData.date).label('month')
        ).distinct().order_by('year', 'month').all()
        
        logger.info(f"Found {len(unique_months)} unique months to process")
        
        total_processed = 0
        total_failed = 0
        
        # Process each month individually
        for year, month in unique_months:
            logger.info(f"\n{'='*60}")
            logger.info(f"Processing banking settlement for {year}-{month:02d}")
            logger.info(f"{'='*60}")
            
            success = processor.process_monthly_banking(year, month)
            
            if success:
                total_processed += 1
                
                # Get overall summary for this month
                summary = processor.get_banking_summary(year, month)
                print(f"\n=== Banking Settlement Summary for {year}-{month:02d} ===")
                print(f"Total Surplus Demand: {summary['total_surplus_demand']}")
                print(f"Total Surplus Generation: {summary['total_surplus_generation']}")
                print(f"Total Matched Settled: {summary['total_matched_settled']}")
                print(f"Total Intra Settlement: {summary['total_intra_settlement']}")
                print(f"Total Inter Settlement: {summary['total_inter_settlement']}")
                print(f"Remaining Surplus Generation: {summary['remaining_surplus_generation']}")
                print(f"Remaining Surplus Demand: {summary['remaining_surplus_demand']}")
                
                # Get slot-based report for this month
                slot_report = processor.generate_slot_based_report(year, month)
                print(f"\n=== Slot-wise Banking Report for {year}-{month:02d} ===")
                
                for slot_name, slot_data in slot_report['slot_summary'].items():
                    print(f"\n{slot_name} ({slot_data['slot_time']}):")
                    print(f"  Records: {slot_data['record_count']}")
                    print(f"  Surplus Generation: {slot_data['total_surplus_generation']}")
                    print(f"  Surplus Demand: {slot_data['total_surplus_demand']}")
                    print(f"  Intra Settlement: {slot_data['total_intra_settlement']}")
                    print(f"  Inter Settlement: {slot_data['total_inter_settlement']}")
                    print(f"  Remaining Generation: {slot_data['remaining_surplus_generation']}")
                    print(f"  Remaining Demand: {slot_data['remaining_surplus_demand']}")
                    print(f"  Settlement Efficiency: {slot_data['settlement_efficiency']}%")
                
                logger.info(f"SUCCESS: Successfully processed {year}-{month:02d}")
            else:
                total_failed += 1
                logger.error(f"FAILED: Failed to process {year}-{month:02d}")
        
        # Final summary
        print(f"\n{'='*60}")
        print(f"FINAL PROCESSING SUMMARY")
        print(f"{'='*60}")
        print(f"Total months processed: {total_processed}")
        print(f"Total months failed: {total_failed}")
        print(f"Success rate: {(total_processed/(total_processed+total_failed)*100):.1f}%" if (total_processed+total_failed) > 0 else "N/A")
        
        if total_failed == 0:
            logger.info("SUCCESS: All months processed successfully!")
        else:
            logger.warning(f"WARNING: {total_failed} months failed processing")
    
    except Exception as e:
        logger.error(f"Critical error in main processing: {str(e)}")
        session.rollback()
    
    finally:
        session.close()


def main_simplified():
    """Simplified main function using the class method"""
    
    # Database connection setup
    DATABASE_URL = "mysql+pymysql://root:test123@localhost/energy_db"
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create banking processor
        processor = MonthlyBankingProcessor(session)
        
        # Process all available months
        results = processor.process_all_available_months()
        
        # Print final summary
        print(f"\n{'='*60}")
        print(f"BANKING SETTLEMENT PROCESSING COMPLETE")
        print(f"{'='*60}")
        print(f"Total months found: {results['total_months']}")
        print(f"Successfully processed: {results['processed_successfully']}")
        print(f"Failed: {len(results['failed_months'])}")
        print(f"Success rate: {results['success_rate']:.1f}%")
        
        if results['failed_months']:
            print(f"\nFailed months:")
            for year, month in results['failed_months']:
                print(f"  - {year}-{month:02d}")
        
        # Print summary for each successful month
        print(f"\n{'='*60}")
        print(f"MONTHLY SUMMARIES")
        print(f"{'='*60}")
        
        for result in results['processing_summary']:
            if result['status'] == 'SUCCESS':
                summary = result['summary']
                print(f"\nSUMMARY {result['year']}-{result['month']:02d}:")
                print(f"   Total Surplus Demand: {summary['total_surplus_demand']}")
                print(f"   Total Surplus Generation: {summary['total_surplus_generation']}")
                print(f"   Total Intra Settlement: {summary['total_intra_settlement']}")
                print(f"   Total Inter Settlement: {summary['total_inter_settlement']}")
                print(f"   Remaining Generation: {summary['remaining_surplus_generation']}")
                print(f"   Remaining Demand: {summary['remaining_surplus_demand']}")
        
    except Exception as e:
        logger.error(f"Critical error in main processing: {str(e)}")
        session.rollback()
    
    finally:
        session.close()


def main_karnataka_demo():
    """Demo function specifically for Karnataka 2025 banking settlement"""
    
    # Database connection setup
    DATABASE_URL = "mysql+pymysql://root:test123@localhost/energy_db"
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create banking processor
        processor = MonthlyBankingProcessor(session)
        
        # Get available months
        available_months = processor.get_available_months()
        
        if not available_months:
            logger.error("No settlement data found in database")
            return
        
        logger.info(f"Karnataka 2025 Banking Settlement Demo")
        logger.info(f"Available months: {available_months}")
        
        # Process and show Karnataka summary for each month
        for year, month in available_months:
            try:
                logger.info(f"\n{'='*80}")
                logger.info(f"PROCESSING KARNATAKA 2025 BANKING FOR {year}-{month:02d}")
                logger.info(f"{'='*80}")
                
                summary = processor.process_and_show_karnataka_summary(year, month)
                
                # Print detailed slot-wise breakdown
                print(f"\n=== Slot-wise Breakdown for {year}-{month:02d} ===")
                for slot_name, slot_data in summary['slot_summary'].items():
                    print(f"\n{slot_name}:")
                    print(f"  Original Surplus Demand: {slot_data['original_surplus_demand']}")
                    print(f"  Original Surplus Generation: {slot_data['original_surplus_generation']}")
                    print(f"  After Intra-TOD Settlement:")
                    print(f"    Demand: {slot_data['demand_after_intra']}")
                    print(f"    Generation: {slot_data['generation_after_intra']}")
                    print(f"  Final (After Inter-TOD Settlement):")
                    print(f"    Grid Consumption: {slot_data['final_grid_consumption']}")
                    print(f"    Surplus Generation: {slot_data['final_surplus_generation']}")
                    print(f"  Settlements:")
                    print(f"    Intra-TOD: {slot_data['intra_settlement']}")
                    print(f"    Inter-TOD: {slot_data['inter_settlement']}")
                
                # Print overall summary
                overall = summary['overall_summary']
                print(f"\n=== Overall Monthly Summary ===")
                print(f"Total Consumption: {overall['total_consumption']}")
                print(f"Grid Consumption: {overall['grid_consumption']}")
                print(f"Renewable Replacement: {overall['replacement']}")
                print(f"Final Surplus Generation: {overall['surplus_generation']}")
                print(f"Total Intra-TOD Settlement: {overall['total_intra_settlement']}")
                print(f"Total Inter-TOD Settlement: {overall['total_inter_settlement']}")
                
                # Calculate replacement percentage
                if overall['total_consumption'] > 0:
                    replacement_pct = float(overall['replacement'] / overall['total_consumption'] * 100)
                    print(f"Renewable Replacement %: {replacement_pct:.2f}%")
                
            except Exception as e:
                logger.error(f"Failed to process {year}-{month:02d}: {str(e)}")
        
    except Exception as e:
        logger.error(f"Critical error in Karnataka demo: {str(e)}")
        session.rollback()
    
    finally:
        session.close()


if __name__ == "__main__":
    # You can choose which version to run:
    
    # Option 1: Detailed version with slot reports
    # main()
    
    # Option 2: Simplified version
    # main_simplified()
    
    # Option 3: Karnataka 2025 Demo (recommended)
    main_karnataka_demo()