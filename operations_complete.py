from models import *
from db_setup import *
import pandas as pd
from sqlalchemy.exc import IntegrityError
import json

def safe_insert(records):
    """Safely insert records with error handling and duplicate prevention"""
    if not records:
        print("No records to insert")
        return
    
    try:
        # Use merge instead of bulk_save_objects to handle duplicates
        for record in records:
            session.merge(record)
        session.commit()
        print(f"Successfully inserted/updated {len(records)} records")
    except IntegrityError as e:
        session.rollback()
        print(f"IntegrityError: {e}")
    except Exception as e:
        session.rollback()
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()

def get_client_info(plant_id: str, type_: str) -> dict:
    """Get client information from client.json"""
    try:
        with open('d:/Harikrishnan/Data Ingestion/client.json', 'r') as f:
            client_data = json.load(f)
        
        # Search for the plant_id in the specified type
        if type_ in client_data:
            for client_name, plants in client_data[type_].items():
                for plant in plants:
                    if plant['plant_id'] == plant_id:
                        return {
                            'client_name': client_name,
                            'plant_name': plant['name']
                        }
        
        # If not found, return default values
        return {'client_name': 'Unknown', 'plant_name': 'Unknown'}
    except Exception as e:
        print(f"Error reading client.json: {e}")
        return {'client_name': 'Unknown', 'plant_name': 'Unknown'}

def prepare_generation_records_api(df: pd.DataFrame, plant_id: str, type_: str = 'solar') -> list:
    """Prepare generation records from API data"""
    # Debug: Print DataFrame info
    print(f"Generation DataFrame shape: {df.shape}")
    print(f"Generation DataFrame columns: {list(df.columns)}")
    print(f"Generation DataFrame head:\n{df.head()}")
    
    # Make a copy to avoid modifying the original
    df = df.copy()
    
    # Rename columns safely - handle both generation and active_power if available
    if len(df.columns) >= 2:
        if len(df.columns) >= 3:
            df.columns = ["datetime", "generation", "active_power"] + list(df.columns[3:])
        else:
            df.columns = ["datetime", "generation"] + list(df.columns[2:])
            df['active_power'] = 0  # Default value if not provided
    else:
        raise ValueError(f"Expected at least 2 columns, got {len(df.columns)}")

    # Ensure datetime format
    df['datetime'] = pd.to_datetime(df['datetime'])

    # Convert non-numeric values to NaN, then fill NaN with 0
    df["generation"] = pd.to_numeric(df["generation"], errors="coerce").fillna(0)
    df["active_power"] = pd.to_numeric(df["active_power"], errors="coerce").fillna(0)

    # Extract date and time from datetime
    df['date'] = df['datetime'].dt.date
    df['time'] = df['datetime'].dt.time

    # Get client information
    client_info = get_client_info(plant_id, type_)
    
    # Add metadata
    df['plant_id'] = plant_id
    df['plant_name'] = client_info['plant_name']
    df['client_name'] = client_info['client_name']
    df['type'] = type_

    # Remove duplicates based on unique key columns (plant_id, date, time, type)
    unique_key_cols = ['plant_id', 'date', 'time', 'type']
    df = df.drop_duplicates(subset=unique_key_cols, keep='last')
    print(f"After removing duplicates: {df.shape[0]} records")

    # Select only the columns needed for TblGeneration model
    required_columns = ['plant_id', 'plant_name', 'client_name', 'type', 'date', 'time', 'generation', 'active_power']
    df_filtered = df[required_columns].copy()

    # Generate ORM-compatible records
    records = [TblGeneration(**row.to_dict()) for _, row in df_filtered.iterrows()]
    return records

def prepare_consumption_records_csv(df: pd.DataFrame) -> list:
    """Prepare consumption records from CSV data"""
    # Debug: Print DataFrame info
    print(f"Consumption DataFrame shape: {df.shape}")
    print(f"Consumption DataFrame columns: {list(df.columns)}")
    print(f"Consumption DataFrame head:\n{df.head()}")
    
    # Make a copy to avoid modifying the original
    df = df.copy()
    
    # Drop the existing 'datetime' column if it exists to avoid conflicts
    if 'datetime' in df.columns:
        df = df.drop(columns=['datetime'])
    
    # Rename columns to match the TblConsumption model
    df = df.rename(columns={
        "time": "datetime",
        "Plant Long Name": "client_name",
        "R.R.No": "cons_unit",  # Using R.R.No as consumption unit identifier
        "Energy_kWh": "consumption"
    })

    # Ensure datetime format
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    # Extract date and time from datetime
    df['date'] = df['datetime'].dt.date
    df['time'] = df['datetime'].dt.time
    
    # Convert NaN consumption values to 0
    df['consumption'] = pd.to_numeric(df['consumption'], errors='coerce').fillna(0)
    
    # Select only the columns that exist in the TblConsumption model (removed location_name)
    required_columns = ['cons_unit', 'client_name', 'date', 'time', 'consumption']
    df_filtered = df[required_columns].copy()
    
    # Remove duplicates based on unique key columns (cons_unit, date, time)
    unique_key_cols = ['cons_unit', 'date', 'time']
    df_filtered = df_filtered.drop_duplicates(subset=unique_key_cols, keep='last')
    print(f"After removing duplicates: {df_filtered.shape[0]} records")
    
    # Generate ORM-compatible records
    records = [TblConsumption(**row.to_dict()) for _, row in df_filtered.iterrows()]
    return records

def insert_generation_from_csv(csv_path: str, plant_id: str, type_: str = 'solar'):
    """Insert generation data from CSV file"""
    df = pd.read_csv(csv_path)
    records = prepare_generation_records_api(df, plant_id, type_)
    safe_insert(records)

def insert_generation_from_api(df: pd.DataFrame, plant_id: str, type_: str = 'solar'):
    """Insert generation data from API DataFrame"""
    records = prepare_generation_records_api(df, plant_id, type_)
    safe_insert(records)

def insert_consumption_from_csv(csv_path: str):
    """Insert consumption data from CSV file"""
    df = pd.read_csv(csv_path)
    records = prepare_consumption_records_csv(df)
    safe_insert(records)

def prepare_consumption_mapping_records(generation_df: pd.DataFrame, consumption_df: pd.DataFrame) -> list:
    """Prepare consumption mapping records - static mapping between clients and consumption units"""
    # Note: The new consumption_mapping table is a static lookup table
    # It maps client_name to cons_unit with location and percentage
    # This function now creates a basic mapping based on available data
    
    # Get unique client names from generation data (assuming plant_name maps to client_name)
    unique_clients = generation_df['plant_name'].unique() if 'plant_name' in generation_df.columns else []
    
    # Get unique consumption units from consumption data
    unique_units = consumption_df['cons_unit'].unique() if 'cons_unit' in consumption_df.columns else []
    
    # Create basic mapping records (you may want to customize this logic)
    mapping_records = []
    
    for i, client in enumerate(unique_clients):
        for j, unit in enumerate(unique_units):
            # Create a basic mapping - you can customize the percentage logic
            percentage = 100.0 / len(unique_units)  # Equal distribution by default
            
            mapping_record = {
                'client_name': client,
                'cons_unit': unit,
                'location_name': f'Location_{i+1}',  # Default location naming
                'percentage': round(percentage, 2)
            }
            mapping_records.append(mapping_record)
    
    # Convert to DataFrame for processing
    df_mapping = pd.DataFrame(mapping_records)
    
    if df_mapping.empty:
        print("Warning: No consumption mapping records could be created")
        return []
    
    # Remove duplicates based on unique key (client_name, cons_unit)
    df_mapping = df_mapping.drop_duplicates(subset=['client_name', 'cons_unit'], keep='last')
    
    # Generate ORM-compatible records
    records = [ConsumptionMapping(**row.to_dict()) for _, row in df_mapping.iterrows()]
    return records

def prepare_settlement_records(generation_df: pd.DataFrame, consumption_df: pd.DataFrame, plant_id: str, type_: str) -> list:
    """Prepare settlement records by calculating surplus/deficit"""
    # Get client information
    client_info = get_client_info(plant_id, type_)
    
    # Merge generation and consumption data on datetime
    merged_df = pd.merge(
        generation_df[['date', 'time', 'generation']].rename(columns={'generation': 'allocated_generation'}),
        consumption_df[['cons_unit', 'date', 'time', 'consumption']],
        on=['date', 'time'],
        how='outer',
        suffixes=('_gen', '_cons')
    )
    
    # Fill NaN values with appropriate defaults
    merged_df['allocated_generation'] = merged_df['allocated_generation'].fillna(0)
    merged_df['consumption'] = merged_df['consumption'].fillna(0)
    merged_df['cons_unit'] = merged_df['cons_unit'].fillna('Unknown')
    
    # Calculate all settlement values
    # deficit: when consumption > allocated_generation (negative value)
    # surplus_demand: when consumption > allocated_generation (positive value)
    # surplus_generation: when allocated_generation > consumption (positive value)
    # settled: minimum of allocated_generation and consumption
    merged_df['deficit'] = merged_df.apply(
        lambda row: row['consumption'] - row['allocated_generation'], axis=1
    )
    merged_df['surplus_demand'] = merged_df.apply(
        lambda row: max(0, row['consumption'] - row['allocated_generation']), axis=1
    )
    merged_df['surplus_generation'] = merged_df.apply(
        lambda row: max(0, row['allocated_generation'] - row['consumption']), axis=1
    )
    merged_df['settled'] = merged_df.apply(
        lambda row: min(row['allocated_generation'], row['consumption']), axis=1
    )
    
    # Add metadata
    merged_df['plant_id'] = plant_id
    merged_df['client_name'] = client_info['client_name']
    merged_df['type'] = type_
    
    # Select required columns for SettlementData model
    required_columns = ['plant_id', 'client_name', 'cons_unit', 'type', 'date', 'time', 'allocated_generation', 'consumption', 'deficit', 'surplus_demand', 'surplus_generation', 'settled']
    df_filtered = merged_df[required_columns].copy()
    
    # Ensure no NaN values remain
    df_filtered['cons_unit'] = df_filtered['cons_unit'].fillna('Unknown')
    df_filtered['allocated_generation'] = df_filtered['allocated_generation'].fillna(0)
    df_filtered['consumption'] = df_filtered['consumption'].fillna(0)
    df_filtered['deficit'] = df_filtered['deficit'].fillna(0)
    df_filtered['surplus_demand'] = df_filtered['surplus_demand'].fillna(0)
    df_filtered['surplus_generation'] = df_filtered['surplus_generation'].fillna(0)
    df_filtered['settled'] = df_filtered['settled'].fillna(0)
    
    # Remove duplicates
    unique_key_cols = ['plant_id', 'date', 'time', 'type']
    df_filtered = df_filtered.drop_duplicates(subset=unique_key_cols, keep='last')
    
    # Generate ORM-compatible records
    records = [SettlementData(**row.to_dict()) for _, row in df_filtered.iterrows()]
    return records

def insert_consumption_mapping_data(generation_df: pd.DataFrame, consumption_df: pd.DataFrame):
    """Insert consumption mapping data"""
    records = prepare_consumption_mapping_records(generation_df, consumption_df)
    safe_insert(records)

def insert_settlement_data(generation_df: pd.DataFrame, consumption_df: pd.DataFrame, plant_id: str, type_: str):
    """Insert settlement data"""
    records = prepare_settlement_records(generation_df, consumption_df, plant_id, type_)
    safe_insert(records)

def insert_plant_data(plant_id: str, type_: str):
    """Insert plant data into tbl_plants"""
    # Get client information
    client_info = get_client_info(plant_id, type_)
    
    # Create plant record
    plant_record = TblPlants(
        plant_id=plant_id,
        plant_name=client_info['plant_name'],
        client_name=client_info['client_name'],
        type=type_
    )
    
    safe_insert([plant_record])
    print(f"Plant data inserted for {plant_id}")

# Test functions
def test_api_generation():
    """Test generation data from API"""
    from integration_utilities import PrescintoIntegrationUtilities
    
    print("Making API call for generation data...")
    m = PrescintoIntegrationUtilities(server='IN', token='1e7d1ee5-2518-43c7-a0ad-3831d82d8507')
    params = ['Daily Energy']
    category = ['Plant']
    plantName = 'IN.INTE.KIDS'
    startDate = '2025-01-01'
    endDate = '2025-06-04'
    condition = {"Daily Energy": "last"}
    
    try:
        dg_df = m.fetchDataV2(plantName, category, params, None, startDate, endDate, granularity='15m', condition=condition)
        
        if dg_df is not None:
            print("API call successful. Attempting to insert data...")
            insert_generation_from_api(dg_df, plantName, 'solar')
        else:
            print("API returned None")
    except Exception as e:
        print(f"Error during API call or insertion: {e}")
        import traceback
        traceback.print_exc()

def test_csv_consumption():
    """Test consumption data from CSV"""
    try:
        print("Testing consumption CSV import...")
        insert_consumption_from_csv("d:/Harikrishnan/Data Ingestion/Consumption data Cloud nine - processed_data.csv")
        print("Consumption data import completed successfully!")
    except Exception as e:
        print(f"Error during CSV consumption import: {e}")
        import traceback
        traceback.print_exc()

def test_complete_workflow():
    """Test complete workflow: generation + consumption + percentage + settlement"""
    try:
        print("=== Testing Complete Workflow ===")
        
        # 1. Load generation data from API
        print("1. Loading generation data from API...")
        from integration_utilities import PrescintoIntegrationUtilities
        
        m = PrescintoIntegrationUtilities(server='IN', token='1e7d1ee5-2518-43c7-a0ad-3831d82d8507')
        params = ['Daily Energy']
        category = ['Plant']
        plantName = 'IN.INTE.KIDS'
        startDate = '2025-01-01'
        endDate = '2025-06-06'  # Small date range for testing
        condition = {"Daily Energy": "last"}

        
        generation_df = m.fetchDataV2(plantName, category, params, None, startDate, endDate, granularity='15m', condition=condition)
        # generation_df.to_csv('Generation_test.csv', index=False)
        
        if generation_df is not None:
            # Insert plant data first
            insert_plant_data(plantName, 'solar')
            print("Plant data inserted successfully!")
            
            insert_generation_from_api(generation_df, plantName, 'solar')
            print("Generation data inserted successfully!")
            
            # 2. Load consumption data from CSV
            print("2. Loading consumption data from CSV...")
            consumption_csv_path = "d:/Harikrishnan/Data Ingestion/Consumption data Cloud nine - processed_data.csv"
            consumption_df = pd.read_csv(consumption_csv_path)
            
            # Filter consumption data to match the same date range
            # Use the existing 'time' column for filtering
            consumption_df['temp_datetime'] = pd.to_datetime(consumption_df['time'])
            consumption_df = consumption_df[
                (consumption_df['temp_datetime'] >= startDate) & 
                (consumption_df['temp_datetime'] <= endDate)
            ]
            # Remove the temporary column
            consumption_df = consumption_df.drop(columns=['temp_datetime'])
            
            if not consumption_df.empty:
                records = prepare_consumption_records_csv(consumption_df)
                safe_insert(records)
                print("Consumption data inserted successfully!")
                
                # 3. Generate consumption mapping data
                print("3. Generating consumption mapping data...")
                # Prepare generation data for mapping calculation
                gen_records = prepare_generation_records_api(generation_df, plantName, 'solar')
                gen_df = pd.DataFrame([record.__dict__ for record in gen_records])
                
                # Prepare consumption data for mapping calculation
                cons_df = pd.DataFrame([record.__dict__ for record in records])
                
                insert_consumption_mapping_data(gen_df, cons_df)
                print("Consumption mapping data inserted successfully!")
                
                # 4. Generate settlement data
                print("4. Generating settlement data...")
                insert_settlement_data(gen_df, cons_df, plantName, 'solar')
                print("Settlement data inserted successfully!")
                
            else:
                print("No consumption data found for the specified date range")
        else:
            print("API returned None for generation data")
            
    except Exception as e:
        print(f"Error during complete workflow test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Operations module loaded successfully!")
    print("Available functions:")
    print("- test_api_generation()")
    print("- test_csv_consumption()")
    print("- test_complete_workflow()")
    print("- insert_generation_from_csv(csv_path, plant_id, type_)")
    print("- insert_generation_from_api(df, plant_id, type_)")
    print("- insert_consumption_from_csv(csv_path)")
    print("- insert_consumption_mapping_data(generation_df, consumption_df)")
    print("- insert_settlement_data(generation_df, consumption_df, plant_id, type_)")
    print("- insert_plant_data(plant_id, type_)")
    
    print("\n=== Testing CSV Consumption Data ===")
    # test_csv_consumption()
    # test_api_generation()
    
    print("\n=== Testing Complete Workflow ===")
    # Uncomment the line below to test the complete workflow
    test_complete_workflow()