"""
Generation Data API Module
This module handles adding generation data from API calls only.
"""

from models import TblGeneration, TblPlants
from db_setup import session
import pandas as pd
from sqlalchemy.exc import IntegrityError
import json
from integration_utilities import PrescintoIntegrationUtilities


def safe_insert(records):
    """Safely insert records with error handling and duplicate prevention"""
    if not records:
        print("No records to insert")
        return
    
    try:
        # Use merge instead of bulk_save_objects to handle duplicates
        for record in records:
            session.merge(record)
        session.commit()
        print(f"Successfully inserted/updated {len(records)} records")
    except IntegrityError as e:
        session.rollback()
        print(f"IntegrityError: {e}")
    except Exception as e:
        session.rollback()
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()


def get_client_info(plant_id: str, type_: str) -> dict:
    """Get client information from client.json"""
    try:
        with open('d:/<PERSON><PERSON><PERSON><PERSON>an/Data Ingestion/client.json', 'r') as f:
            client_data = json.load(f)
        
        # Search for the plant_id in the specified type
        if type_ in client_data:
            for client_name, plants in client_data[type_].items():
                for plant in plants:
                    if plant['plant_id'] == plant_id:
                        return {
                            'client_name': client_name,
                            'plant_name': plant['name']
                        }
        
        # If not found, return default values
        return {'client_name': 'Unknown', 'plant_name': 'Unknown'}
    except Exception as e:
        print(f"Error reading client.json: {e}")
        return {'client_name': 'Unknown', 'plant_name': 'Unknown'}


def insert_plant_data(plant_id: str, type_: str):
    """Insert plant data into tbl_plants"""
    # Get client information
    client_info = get_client_info(plant_id, type_)
    
    # Create plant record
    plant_record = TblPlants(
        plant_id=plant_id,
        plant_name=client_info['plant_name'],
        client_name=client_info['client_name'],
        type=type_
    )
    
    safe_insert([plant_record])
    print(f"Plant data inserted for {plant_id}")


def prepare_generation_records_api(df: pd.DataFrame, plant_id: str, type_: str = 'solar') -> list:
    """Prepare generation records from API data"""
    # Debug: Print DataFrame info
    print(f"Generation DataFrame shape: {df.shape}")
    print(f"Generation DataFrame columns: {list(df.columns)}")
    print(f"Generation DataFrame head:\n{df.head()}")
    
    # Make a copy to avoid modifying the original
    df = df.copy()
    
    # Rename columns safely - handle both generation and active_power if available
    if len(df.columns) >= 2:
        if len(df.columns) >= 3:
            df.columns = ["datetime", "generation", "active_power"] + list(df.columns[3:])
        else:
            df.columns = ["datetime", "generation"] + list(df.columns[2:])
            df['active_power'] = 0  # Default value if not provided
    else:
        raise ValueError(f"Expected at least 2 columns, got {len(df.columns)}")

    # Ensure datetime format
    df['datetime'] = pd.to_datetime(df['datetime'])

    # Convert non-numeric values to NaN, then fill NaN with 0
    df["generation"] = pd.to_numeric(df["generation"], errors="coerce").fillna(0)
    df["active_power"] = pd.to_numeric(df["active_power"], errors="coerce").fillna(0)

    # Extract date and time from datetime
    df['date'] = df['datetime'].dt.date
    df['time'] = df['datetime'].dt.time

    # Get client information
    client_info = get_client_info(plant_id, type_)
    
    # Add metadata
    df['plant_id'] = plant_id
    df['plant_name'] = client_info['plant_name']
    df['client_name'] = client_info['client_name']
    df['type'] = type_

    # Remove duplicates based on unique key columns (plant_id, date, time, type)
    unique_key_cols = ['plant_id', 'date', 'time', 'type']
    df = df.drop_duplicates(subset=unique_key_cols, keep='last')
    print(f"After removing duplicates: {df.shape[0]} records")

    # Select only the columns needed for TblGeneration model
    required_columns = ['plant_id', 'plant_name', 'client_name', 'type', 'date', 'time', 'generation', 'active_power']
    df_filtered = df[required_columns].copy()

    # Generate ORM-compatible records
    records = [TblGeneration(**row.to_dict()) for _, row in df_filtered.iterrows()]
    return records


def insert_generation_from_api(df: pd.DataFrame, plant_id: str, type_: str = 'solar'):
    """Insert generation data from API DataFrame"""
    records = prepare_generation_records_api(df, plant_id, type_)
    safe_insert(records)


def fetch_and_insert_generation_data(plant_id: str, 
                                   start_date: str, 
                                   end_date: str, 
                                   type_: str = 'solar',
                                   granularity: str = '15m',
                                   server: str = 'IN',
                                   token: str = '5f142bbb-02dc-481d-9cba-452efc48b304'):
    """
    Fetch generation data from API and insert into database
    
    Args:
        plant_id (str): Plant identifier (e.g., 'IN.INTE.KIDS')
        start_date (str): Start date in 'YYYY-MM-DD' format
        end_date (str): End date in 'YYYY-MM-DD' format
        type_ (str): Plant type ('solar' or 'wind')
        granularity (str): Data granularity ('15m', '1h', '1d', etc.)
        server (str): Server identifier
        token (str): API token
    """
    try:
        print(f"=== Fetching Generation Data for {plant_id} ===")
        print(f"Date range: {start_date} to {end_date}")
        print(f"Type: {type_}, Granularity: {granularity}")
        
        # Initialize API client
        m = PrescintoIntegrationUtilities(server=server, token=token)
        
        # API parameters
        params = ['Daily Energy']
        category = ['Plant']
        condition = {"Daily Energy": "last"}
        
        # Fetch data from API
        print("Making API call...")
        generation_df = m.fetchDataV2(
            plant_id, 
            category, 
            params, 
            None, 
            start_date, 
            end_date, 
            granularity=granularity, 
            condition=condition
        )
        
        if generation_df is not None and not generation_df.empty:
            print(f"API call successful. Retrieved {len(generation_df)} records")
            
            # Insert plant data first (if not exists)
            print("Inserting/updating plant data...")
            insert_plant_data(plant_id, type_)
            
            # Insert generation data
            print("Inserting generation data...")
            insert_generation_from_api(generation_df, plant_id, type_)
            
            print("✅ Generation data insertion completed successfully!")
            return True
            
        else:
            print("❌ API returned no data")
            return False
            
    except Exception as e:
        print(f"❌ Error during API call or insertion: {e}")
        import traceback
        traceback.print_exc()
        return False


def batch_insert_generation_data(plants_config: list):
    """
    Insert generation data for multiple plants
    
    Args:
        plants_config (list): List of dictionaries with plant configuration
        Example:
        [
            {
                'plant_id': 'IN.INTE.KIDS',
                'start_date': '2025-01-01',
                'end_date': '2025-01-10',
                'type': 'solar'
            },
            {
                'plant_id': 'IN.WIND.PLANT1',
                'start_date': '2025-01-01',
                'end_date': '2025-01-10',
                'type': 'wind'
            }
        ]
    """
    print(f"=== Batch Processing {len(plants_config)} Plants ===")
    
    success_count = 0
    failed_plants = []
    
    for i, config in enumerate(plants_config, 1):
        print(f"\n[{i}/{len(plants_config)}] Processing {config['plant_id']}...")
        
        success = fetch_and_insert_generation_data(
            plant_id=config['plant_id'],
            start_date=config['start_date'],
            end_date=config['end_date'],
            type_=config.get('type', 'solar'),
            granularity=config.get('granularity', '15m')
        )
        
        if success:
            success_count += 1
        else:
            failed_plants.append(config['plant_id'])
    
    print(f"\n=== Batch Processing Complete ===")
    print(f"✅ Successful: {success_count}/{len(plants_config)}")
    if failed_plants:
        print(f"❌ Failed: {failed_plants}")


# Example usage and test functions
def test_single_plant():
    """Test generation data for a single plant"""
    plant_id = 'IN.INTE.KIDS'
    start_date = '2025-01-01'
    end_date = '2025-06-05'
    type_ = 'solar'
    
    success = fetch_and_insert_generation_data(
        plant_id=plant_id,
        start_date=start_date,
        end_date=end_date,
        type_=type_
    )
    
    if success:
        print("✅ Single plant test completed successfully!")
    else:
        print("❌ Single plant test failed!")


def test_multiple_plants():
    """Test generation data for multiple plants"""
    plants_config = [
        {
            'plant_id': 'IN.INTE.KIDS',
            'start_date': '2025-01-01',
            'end_date': '2025-01-03',
            'type': 'solar'
        },
        # Add more plants as needed
        # {
        #     'plant_id': 'IN.WIND.PLANT1',
        #     'start_date': '2025-01-01',
        #     'end_date': '2025-01-03',
        #     'type': 'wind'
        # }
    ]
    
    batch_insert_generation_data(plants_config)


if __name__ == "__main__":
    print("=== Generation Data API Module ===")
    print("Available functions:")
    print("- fetch_and_insert_generation_data(plant_id, start_date, end_date, type_, granularity)")
    print("- batch_insert_generation_data(plants_config)")
    print("- test_single_plant()")
    print("- test_multiple_plants()")
    print("- insert_plant_data(plant_id, type_)")
    
    print("\n=== Running Test ===")
    # Uncomment the line below to test
    test_single_plant()