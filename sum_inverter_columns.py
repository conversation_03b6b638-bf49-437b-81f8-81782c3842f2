import pandas as pd

def add_total_generation_column(csv_file_path, output_file_path=None):
    """
    Read CSV file and add a column that sums all columns after the time column.
    
    Args:
        csv_file_path (str): Path to the input CSV file
        output_file_path (str, optional): Path for output CSV file. If None, overwrites input file.
    
    Returns:
        pandas.DataFrame: DataFrame with the new total column added
    """
    
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Print original column structure for verification
    print("Original columns:", df.columns.tolist())
    print("Data shape:", df.shape)
    
    # Get all columns after the first column (Date & Time)
    time_column = df.columns[0]  # First column (Date & Time)
    numeric_columns = df.columns[1:]  # All columns after the time column
    
    print(f"Time column: {time_column}")
    print(f"Columns to sum: {numeric_columns.tolist()}")
    
    # Convert numeric columns to numeric type (in case they're strings)
    for col in numeric_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Sum all numeric columns for each row
    df['Total_Generation'] = df[numeric_columns].sum(axis=1)
    
    print(f"Added 'Total_Generation' column")
    print("Sample of the data with new column:")
    print(df.head())
    
    # Save to output file
    if output_file_path is None:
        output_file_path = csv_file_path
    
    df.to_csv(output_file_path, index=False)
    print(f"Updated data saved to: {output_file_path}")
    
    return df

# Alternative version if you want to specify column names explicitly
def add_total_generation_explicit(csv_file_path, output_file_path=None):
    """
    Read CSV file and add a column that sums specific inverter columns.
    This version explicitly names the columns to sum.
    """
    
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Define the inverter columns explicitly
    inverter_columns = [f'INV_{i}' for i in range(1, 17)]  # INV_1 to INV_16
    
    # Verify all columns exist
    missing_columns = [col for col in inverter_columns if col not in df.columns]
    if missing_columns:
        print(f"Warning: Missing columns: {missing_columns}")
        inverter_columns = [col for col in inverter_columns if col in df.columns]
    
    print(f"Summing columns: {inverter_columns}")
    
    # Convert to numeric
    for col in inverter_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Sum the inverter columns
    df['Total_Generation'] = df[inverter_columns].sum(axis=1)
    
    print("Sample data with total:")
    print(df[['Date & Time'] + inverter_columns[-3:] + ['Total_Generation']].head())
    
    # Save to output file
    if output_file_path is None:
        output_file_path = csv_file_path
    
    df.to_csv(output_file_path, index=False)
    print(f"Updated data saved to: {output_file_path}")
    
    return df

if __name__ == "__main__":
    # File paths
    input_file = "d:/Harikrishnan/Data Ingestion/scada_combined_data.csv"
    output_file = "d:/Harikrishnan/Data Ingestion/scada_combined_data_with_total.csv"
    
    print("=== Method 1: Sum all columns after time column ===")
    df1 = add_total_generation_column(input_file, output_file)
    
    print("\n" + "="*60 + "\n")
    
    print("=== Method 2: Sum specific inverter columns ===")
    output_file2 = "d:/Harikrishnan/Data Ingestion/scada_combined_data_with_total_explicit.csv"
    df2 = add_total_generation_explicit(input_file, output_file2)
    
    # Show statistics
    print(f"\nStatistics:")
    print(f"Total records: {len(df1)}")
    print(f"Maximum total generation: {df1['Total_Generation'].max()}")
    print(f"Average total generation: {df1['Total_Generation'].mean():.2f}")
    print(f"Records with zero generation: {(df1['Total_Generation'] == 0).sum()}")