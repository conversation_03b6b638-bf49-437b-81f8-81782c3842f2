import pandas as pd
import numpy as np
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from models import TblGeneration, TblConsumption
from db_setup import engine, SessionLocal
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_gil_generation_consumption_data():
    """
    Read data from GIL_GENERATION_CONSUMPTION.csv and insert into both 
    tbl_generation and tbl_consumption tables
    """
    try:
        # Read the CSV file
        csv_file_path = "GIL_GENERATION_CONSUMPTION.csv"
        logger.info(f"Reading CSV file: {csv_file_path}")
        
        df = pd.read_csv(csv_file_path)
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Plant parameters
        plant_id = "IN.INTE.GIL"
        client_name = "GIL"
        plant_name = "GIL"
        type_value = "wind"
        
        # Create session
        session = SessionLocal()
        
        # Process each row
        generation_inserted = 0
        consumption_inserted = 0
        skipped_count = 0
        
        for index, row in df.iterrows():
            try:
                # Parse the datetime from Date and Slot columns
                date_str = row['Date']
                slot_str = row['Slot']
                
                if pd.isna(date_str) or pd.isna(slot_str):
                    logger.debug(f"Skipping row {index} due to missing date or slot")
                    skipped_count += 1
                    continue
                
                try:
                    # Combine date and slot to create datetime
                    datetime_str = f"{date_str} {slot_str}"
                    dt = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M')
                except (ValueError, TypeError) as e:
                    logger.debug(f"Skipping row {index} due to invalid datetime format: {datetime_str}")
                    skipped_count += 1
                    continue
                
                # Process Generation data
                generation_value = row['Generation']
                if not pd.isna(generation_value):
                    try:
                        generation_value = float(generation_value)
                        
                        # Check if generation record already exists
                        existing_gen_record = session.query(TblGeneration).filter_by(
                            plant_id=plant_id,
                            date=dt.date(),
                            time=dt.time(),
                            type=type_value
                        ).first()
                        
                        if not existing_gen_record:
                            # Create new generation record
                            generation_record = TblGeneration(
                                plant_id=plant_id,
                                plant_name=plant_name,
                                client_name=client_name,
                                type=type_value,
                                date=dt.date(),
                                time=dt.time(),
                                generation=generation_value,
                                active_power=None,
                                pr=None,
                                poa=None,
                                avg_wind_speed=None
                            )
                            
                            session.add(generation_record)
                            generation_inserted += 1
                        else:
                            logger.debug(f"Generation record already exists for {datetime_str}")
                            
                    except (ValueError, TypeError) as e:
                        logger.debug(f"Invalid generation value at row {index}: {generation_value}")
                
                # Process Consumption data
                consumption_value = row['Consumption']
                if not pd.isna(consumption_value):
                    try:
                        # Handle comma-separated values (e.g., "1,618.68")
                        if isinstance(consumption_value, str):
                            consumption_value = consumption_value.replace(',', '')
                        consumption_value = float(consumption_value)
                        
                        # For consumption, we'll use the plant_id as cons_unit
                        cons_unit = plant_id
                        
                        # Check if consumption record already exists
                        existing_cons_record = session.query(TblConsumption).filter_by(
                            cons_unit=cons_unit,
                            date=dt.date(),
                            time=dt.time()
                        ).first()
                        
                        if not existing_cons_record:
                            # Create new consumption record
                            consumption_record = TblConsumption(
                                cons_unit=cons_unit,
                                client_name=client_name,
                                date=dt.date(),
                                time=dt.time(),
                                consumption=consumption_value
                            )
                            
                            session.add(consumption_record)
                            consumption_inserted += 1
                        else:
                            logger.debug(f"Consumption record already exists for {datetime_str}")
                            
                    except (ValueError, TypeError) as e:
                        logger.debug(f"Invalid consumption value at row {index}: {consumption_value}")
                
                # Commit every 100 records for better performance
                if (generation_inserted + consumption_inserted) % 100 == 0:
                    session.commit()
                    logger.info(f"Committed {generation_inserted} generation and {consumption_inserted} consumption records...")
                    
            except Exception as e:
                logger.error(f"Error processing row {index}: {e}")
                session.rollback()
                continue
        
        # Final commit
        session.commit()
        logger.info(f"Data insertion completed successfully!")
        logger.info(f"Total generation records inserted: {generation_inserted}")
        logger.info(f"Total consumption records inserted: {consumption_inserted}")
        logger.info(f"Total records skipped (duplicates and datetime issues): {skipped_count}")
        
    except Exception as e:
        logger.error(f"Error in add_gil_generation_consumption_data: {e}")
        if 'session' in locals():
            session.rollback()
        raise
    finally:
        if 'session' in locals():
            session.close()

def verify_inserted_data():
    """
    Verify the inserted data by checking a few records
    """
    try:
        session = SessionLocal()
        
        # Get count of generation records for GIL plant
        gen_count = session.query(TblGeneration).filter_by(
            plant_id='IN.INTE.GIL',
            type='wind'
        ).count()
        
        logger.info(f"Total generation records found for plant IN.INTE.GIL: {gen_count}")
        
        # Get count of consumption records for GIL plant
        cons_count = session.query(TblConsumption).filter_by(
            cons_unit='IN.INTE.GIL'
        ).count()
        
        logger.info(f"Total consumption records found for cons_unit IN.INTE.GIL: {cons_count}")
        
        # Get first few generation records
        first_gen_records = session.query(TblGeneration).filter_by(
            plant_id='IN.INTE.GIL',
            type='wind'
        ).limit(5).all()
        
        logger.info("First 5 generation records:")
        for record in first_gen_records:
            logger.info(f"Date: {record.date}, Time: {record.time}, Generation: {record.generation}")
        
        # Get first few consumption records
        first_cons_records = session.query(TblConsumption).filter_by(
            cons_unit='IN.INTE.GIL'
        ).limit(5).all()
        
        logger.info("First 5 consumption records:")
        for record in first_cons_records:
            logger.info(f"Date: {record.date}, Time: {record.time}, Consumption: {record.consumption}")
            
    except Exception as e:
        logger.error(f"Error in verify_inserted_data: {e}")
    finally:
        if 'session' in locals():
            session.close()

if __name__ == "__main__":
    logger.info("Starting GIL generation and consumption data insertion...")
    
    # Add the data
    add_gil_generation_consumption_data()
    
    # Verify the insertion
    logger.info("Verifying inserted data...")
    verify_inserted_data()
    
    logger.info("Process completed!")