import pandas as pd
from datetime import timed<PERSON>ta

def process_energy_data_to_15min_csv(
    raw_data, 
    columns, 
    days_in_month, 
    time_slots,
    plant_id=3348,
    plant_long_name="Kids Clinic India Limited",
    plant_short_name="IN.INTE.KIDS",
    account_id=55,
    account_name="Integrum",
    output_filename="15min_interval_energy_data.csv",
    tz_str='Asia/Kolkata'
):
    """
    Complete pipeline to process raw energy data into 15-minute interval CSV.
    
    Args:
        raw_data: List of lists containing energy consumption data
        columns: Column names for the raw data
        days_in_month: Dictionary mapping month names to number of days
        time_slots: List of time slot dictionaries with start, end, and percentage
        plant_id: Plant identifier
        plant_long_name: Full plant name
        plant_short_name: Short plant name
        account_id: Account identifier
        account_name: Account name
        output_filename: Name of output CSV file
        tz_str: Timezone string for data localization
    
    Returns:
        DataFrame: Processed 15-minute interval data
    """
    
    # Step 1: Create initial DataFrame and melt month columns
    df = pd.DataFrame(raw_data, columns=columns)
    
    df_melt = df.melt(
        id_vars=[c for c in columns if c not in days_in_month],
        value_vars=list(days_in_month),
        var_name='Month',
        value_name='Total_kWh'
    )
    
    # Step 2: Calculate daily kWh
    df_melt['Daily_kWh'] = df_melt.apply(
        lambda r: (r['Total_kWh'] / days_in_month[r['Month']]) if pd.notna(r['Total_kWh']) else 0,
        axis=1
    )
    
    # Step 3: Helper function for time slot hours
    def hours_in_slot(start, end):
        return list(range(start, 24)) + list(range(0, end)) if end <= start else list(range(start, end))
    
    # Step 4: Generate 15-minute interval records
    records = []
    for _, row in df_melt[df_melt['Daily_kWh'] > 0].iterrows():
        month_str = row['Month']
        dates = pd.date_range(
            start=pd.to_datetime(f'01-{month_str}', format='%d-%b-%y'),
            periods=days_in_month[month_str],
            freq='D'
        )
        
        for date in dates:
            for slot in time_slots:
                hrs = hours_in_slot(slot['start'], slot['end'])
                slot_total_energy = row['Daily_kWh'] * slot['pct'] / 100
                energy_per_interval = slot_total_energy / (len(hrs) * 4)  # 4 intervals per hour
                
                for hr in hrs:
                    base = pd.Timestamp(date.year, date.month, date.day, hr)
                    for q in range(0, 60, 15):  # 15-minute intervals
                        ts = base + timedelta(minutes=q)
                        records.append({
                            **{c: row[c] for c in df_melt.columns if c not in ['Month', 'Total_kWh', 'Daily_kWh']},
                            'time_ts': ts,
                            'Energy_kWh': round(energy_per_interval, 4)
                        })
    
    # Step 5: Create DataFrame and format timestamps
    out = pd.DataFrame(records)
    
    # Localize and format time
    out['time'] = (
        out['time_ts']
        .dt.tz_localize('UTC')
        .dt.tz_convert(tz_str)
        .apply(lambda x: f"{x.strftime('%Y-%m-%dT%H:%M:%S')}Z" + x.strftime('%z')[:3] + ':' + x.strftime('%z')[3:])
    )
    
    out['Date'] = out['time_ts'].dt.date
    
    # Step 6: Add plant and account information
    out["Plant ID"] = plant_id
    out["Plant Long Name"] = plant_long_name
    out["Plant Short Name"] = plant_short_name
    out["Account ID"] = account_id
    out["Account Name"] = account_name
    
    # Step 7: Add date-related fields
    parsed_dates = pd.to_datetime(out["Date"], format="%Y-%m-%d")
    out["Datevalue"] = parsed_dates.dt.strftime("%d-%b-%y")
    out["Month No"] = parsed_dates.dt.month
    out["Month Name"] = parsed_dates.dt.strftime("%b")
    out["Hour No"] = out['time_ts'].dt.hour
    
    # Step 8: Clean up and reorder columns
    out = out.drop(columns=['time_ts'])
    
    # Reorder columns for final output
    final_columns = [
        "Plant ID", "Plant Long Name", "Plant Short Name", "Account ID", "Account Name",
        "Consumer", "Type", "R.R.No", "Division", 
        "Datevalue", "Month No", "Month Name", "Hour No", "Date", "time", "Energy_kWh"
    ]
    
    # Only include columns that exist in the DataFrame
    available_columns = [col for col in final_columns if col in out.columns]
    remaining_columns = [col for col in out.columns if col not in available_columns]
    
    out = out[available_columns + remaining_columns]
    
    # Step 9: Sort data
    out = out.sort_values(['Consumer', 'R.R.No', 'Date', 'time']).reset_index(drop=True)
    
    # Step 10: Save to CSV
    out.to_csv(output_filename, index=False)
    print(f"15-minute interval data saved to: {output_filename}")
    
    return out


# === USAGE EXAMPLE ===
if __name__ == "__main__":
    # Input data
    raw_data = [
        [1, 'Kids Clinic India Limited', 'Captive', 'C2HT-136', 'Malleswaram', 45220, 45457, 53318, 57674, 55864],
        [2, 'Kids Clinic India Limited', 'Captive', 'S13HT-87', 'ELECTRONIC CITY', 55463, 56673, 67099, 40651, 66151],
        [3, 'Kids Clinic India Limited', 'Captive', 'S12HT-99', 'KANAKAPURA', 37649, 41434, 51469, 84618, 50721],
        [4, 'Kids Clinic India Limited', 'Captive', 'S11HT-124', 'BELLANDUR', 36222, 37562, 47046, 66192, 49250],
        [5, 'Kids Clinic India Limited', 'Captive', 'S11HT-419', 'SARJAPURA', 39944, 43224, 53210, 29022, 55200],
        [6, 'Kids Clinic India Limited', 'Captive', 'C8HT-111', 'SAHAKAR NAGAR', 46288, 46856, 63174, 56010, 61860],
        [7, 'Kids Clinic India Limited', 'Captive', 'E8HT-203', 'HRBR UNIT', 35507, 36389, 51818, 67539, 48479],
        [8, 'Kids Clinic India Limited', 'Captive', 'E4HT-355', 'WHITEFIELD', 57520, 58820, 72000, 53758, 75760],
        [9, 'Kids Clinic India Limited', 'Captive', 'S11BHT 406', 'BELLANDUR CORP. OFFICE', 21892, 23529, 27740, 76880, 27903],
        [10, 'Kids Clinic India Limited', 'Captive', 'C8HT-135', 'THANISANDRA', 43750, 49742, 53512, 63301, 62107],
        [11, 'Kids Clinic India Limited', 'Captive', 'E6HT209', 'Old Airport Road', None, None, None, 89500, 86362]
    ]
    
    columns = ['Sl.No', 'Consumer', 'Type', 'R.R.No', 'Division', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25']
    days_in_month = {'Jan-25': 31, 'Feb-25': 28, 'Mar-25': 31, 'Apr-25': 30, 'May-25': 31}
    time_slots = [
        {'start': 22, 'end': 6,  'pct': 25.78},
        {'start': 6,  'end': 10, 'pct': 16.52},
        {'start': 10, 'end': 18, 'pct': 38.98},
        {'start': 18, 'end': 22, 'pct': 18.72},
    ]
    
    # Process data and generate CSV
    result_df = process_energy_data_to_15min_csv(
        raw_data=raw_data,
        columns=columns,
        days_in_month=days_in_month,
        time_slots=time_slots,
        output_filename="final_15min_energy_data.csv"
    )
    
    # Preview the results
    print("\nFirst 10 rows of processed data:")
    print(result_df.head(10))
    print(f"\nTotal records generated: {len(result_df)}")
    print(f"Date range: {result_df['Date'].min()} to {result_df['Date'].max()}")
    print(f"Columns: {list(result_df.columns)}")