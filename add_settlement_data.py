import pandas as pd
from sqlalchemy import create_engine, select, func
from sqlalchemy.orm import sessionmaker
from models import TblGeneration, TblConsumption, ConsumptionMapping, SettlementData, Base
from datetime import datetime, timedelta
import logging
from decimal import Decimal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SettlementDataInserter:
    def __init__(self, database_url="mysql+pymysql://root:test123@localhost/energy_db"):
        """
        Initialize the Settlement Data Inserter
        
        Args:
            database_url (str): Database connection URL
        """
        self.engine = create_engine(database_url)
        self.Session = sessionmaker(bind=self.engine)
        
    def process_settlement_data(self):
        """
        Process and insert settlement data from tbl_generation and tbl_consumption
        using consumption_mapping to allocate generation
        
        Args:
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Starting settlement data processing")
            
            
            session = self.Session()
            
            # Build generation query
            gen_query = select(TblGeneration)
            
            
            # Execute generation query
            generation_records = session.execute(gen_query).scalars().all()
            logger.info(f"Found {len(generation_records)} generation records")
            
            # Process each generation record
            records_added = 0
            records_updated = 0
            records_skipped = 0
            
            for gen_record in generation_records:
                try:
                    # Get consumption mapping for this client
                    mapping_query = select(ConsumptionMapping).where(
                        ConsumptionMapping.client_name == gen_record.client_name
                    )
                    
                    mappings = session.execute(mapping_query).scalars().all()
                    
                    if not mappings:
                        logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
                        records_skipped += 1
                        continue
                    
                    # Process each mapping to allocate generation
                    for mapping in mappings:
                        # Calculate allocated generation based on percentage
                        if gen_record.supplied is not None:
                            allocated_generation = Decimal(gen_record.supplied) * (Decimal(mapping.percentage) / Decimal(100))
                        else:
                            allocated_generation = Decimal(0)
                        
                        # Find matching consumption record
                        cons_query = select(TblConsumption).where(
                            TblConsumption.client_name == gen_record.client_name,
                            TblConsumption.cons_unit == mapping.cons_unit,
                            TblConsumption.date == gen_record.date,
                            TblConsumption.time == gen_record.time
                        )
                        
                        cons_record = session.execute(cons_query).scalars().first()
                        
                        # Set consumption value (0 if no matching record found)
                        consumption = Decimal(cons_record.consumption) if cons_record and cons_record.consumption is not None else Decimal(0)
                        
                        # Calculate deficit and surplus
                        deficit = allocated_generation - consumption  # Positive when generation > consumption, negative when consumption > generation
                        surplus_demand = max(Decimal(0), -deficit)    # Demand that wasn't met by allocated generation (when deficit is negative)
                        surplus_generation = max(Decimal(0), allocated_generation - consumption)  # Generation that exceeds consumption
                        
                        # Check if settlement record already exists
                        existing = session.query(SettlementData).filter_by(
                            plant_id=gen_record.plant_id,
                            client_name=gen_record.client_name,
                            cons_unit=mapping.cons_unit,
                            date=gen_record.date,
                            time=gen_record.time,
                            type=gen_record.type
                        ).first()
                        
                        if existing:
                            # Update existing record
                            existing.allocated_generation = allocated_generation
                            existing.consumption = consumption
                            existing.deficit = deficit
                            existing.surplus_demand = surplus_demand
                            existing.surplus_generation = surplus_generation
                            existing.settled = min(allocated_generation, consumption)
                            records_updated += 1
                        else:
                            # Create new record
                            settled = min(allocated_generation, consumption)
                            new_record = SettlementData(
                                plant_id=gen_record.plant_id,
                                client_name=gen_record.client_name,
                                cons_unit=mapping.cons_unit,
                                type=gen_record.type,
                                date=gen_record.date,
                                time=gen_record.time,
                                allocated_generation=allocated_generation,
                                consumption=consumption,
                                deficit=deficit,
                                surplus_demand=surplus_demand,
                                surplus_generation=surplus_generation,
                                settled=settled
                            )
                            session.add(new_record)
                            records_added += 1
                
                except Exception as e:
                    logger.error(f"Error processing generation record: {e}")
                    records_skipped += 1
                    continue
            
            # Process consumption records that don't have matching generation records
            cons_query = select(TblConsumption)
            
            
            consumption_records = session.execute(cons_query).scalars().all()
            logger.info(f"Found {len(consumption_records)} consumption records")
            
            for cons_record in consumption_records:
                try:
                    # Check if there's already a settlement record for this consumption
                    existing_settlement = session.query(SettlementData).filter(
                        SettlementData.client_name == cons_record.client_name,
                        SettlementData.cons_unit == cons_record.cons_unit,
                        SettlementData.date == cons_record.date,
                        SettlementData.time == cons_record.time
                    ).first()
                    
                    if not existing_settlement:
                        # Find a mapping for this consumption unit
                        mapping_query = select(ConsumptionMapping).where(
                            ConsumptionMapping.client_name == cons_record.client_name,
                            ConsumptionMapping.cons_unit == cons_record.cons_unit
                        )
                        
                        mapping = session.execute(mapping_query).scalars().first()
                        
                        if not mapping:
                            logger.warning(f"No mapping found for consumption unit: {cons_record.cons_unit}")
                            records_skipped += 1
                            continue
                        
                        # Find a matching plant_id from generation records for this client
                        plant_query = select(TblGeneration.plant_id, TblGeneration.type).where(
                            TblGeneration.client_name == cons_record.client_name
                        ).limit(1)
                        
                        plant_result = session.execute(plant_query).first()
                        
                        if plant_result:
                            plant_id, plant_type = plant_result
                            
                            # Create new settlement record with only consumption data
                            consumption = Decimal(cons_record.consumption) if cons_record.consumption is not None else Decimal(0)
                            # When no generation is allocated, deficit equals consumption, surplus_demand equals consumption
                            deficit = consumption  # consumption - 0 (allocated_generation)
                            surplus_demand = consumption  # All consumption is unmet demand
                            new_record = SettlementData(
                                plant_id=plant_id,
                                client_name=cons_record.client_name,
                                cons_unit=cons_record.cons_unit,
                                type=plant_type,
                                date=cons_record.date,
                                time=cons_record.time,
                                allocated_generation=Decimal(0),
                                consumption=consumption,
                                deficit=deficit,
                                surplus_demand=surplus_demand,
                                surplus_generation=Decimal(0),
                                settled=Decimal(0)
                            )
                            session.add(new_record)
                            records_added += 1
                
                except Exception as e:
                    logger.error(f"Error processing consumption record: {e}")
                    records_skipped += 1
                    continue
            
            # Commit changes
            session.commit()
            session.close()
            
            logger.info(f"✅ Settlement data processing completed:")
            logger.info(f"- {records_added} new records added")
            logger.info(f"- {records_updated} records updated")
            logger.info(f"- {records_skipped} records skipped")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error processing settlement data: {e}")
            import traceback
            traceback.print_exc()
            return False

    def recalculate_surplus_deficit(self, start_date=None, end_date=None, client_name=None):
        """
        Recalculate surplus/deficit for existing settlement data
        
        Args:
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Recalculating surplus/deficit for settlement data")
            
            session = self.Session()
            
            # Build query
            query = session.query(SettlementData)
            
            if start_date:
                query = query.filter(SettlementData.date >= start_date)
            if end_date:
                query = query.filter(SettlementData.date <= end_date)
            if client_name:
                query = query.filter(SettlementData.client_name == client_name)
            
            records = query.all()
            updated_count = 0
            
            for record in records:
                allocated_generation = Decimal(record.allocated_generation) if record.allocated_generation is not None else Decimal(0)
                consumption = Decimal(record.consumption) if record.consumption is not None else Decimal(0)
                
                # Calculate all settlement values
                deficit = allocated_generation - consumption
                surplus_demand = max(Decimal(0), -deficit)
                surplus_generation = max(Decimal(0), allocated_generation - consumption)
                settled = min(allocated_generation, consumption)
                
                # Update record
                record.deficit = deficit
                record.surplus_demand = surplus_demand
                record.surplus_generation = surplus_generation
                record.settled = settled
                
                updated_count += 1
            
            session.commit()
            session.close()
            
            logger.info(f"✅ Updated surplus/deficit for {updated_count} records")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error recalculating surplus/deficit: {e}")
            import traceback
            traceback.print_exc()
            return False

    def recalculate_generation_allocation(self, start_date=None, end_date=None, client_name=None):
        """
        Recalculate generation allocation based on consumption_mapping percentages
        
        Args:
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Recalculating generation allocation based on mapping percentages")
            
            session = self.Session()
            
            # Build generation query
            gen_query = select(TblGeneration)
            if start_date:
                gen_query = gen_query.where(TblGeneration.date >= start_date)
            if end_date:
                gen_query = gen_query.where(TblGeneration.date <= end_date)
            if client_name:
                gen_query = gen_query.where(TblGeneration.client_name == client_name)
            
            # Execute generation query
            generation_records = session.execute(gen_query).scalars().all()
            logger.info(f"Found {len(generation_records)} generation records to reallocate")
            
            records_updated = 0
            
            for gen_record in generation_records:
                try:
                    # Get consumption mapping for this client
                    mapping_query = select(ConsumptionMapping).where(
                        ConsumptionMapping.client_name == gen_record.client_name
                    )
                    
                    mappings = session.execute(mapping_query).scalars().all()
                    
                    if not mappings:
                        logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
                        continue
                    
                    # Process each mapping to allocate generation
                    for mapping in mappings:
                        # Calculate allocated generation based on percentage
                        if gen_record.supplied is not None:
                            allocated_generation = Decimal(gen_record.supplied) * (Decimal(mapping.percentage) / Decimal(100))
                        else:
                            allocated_generation = Decimal(0)
                        
                        # Find settlement record
                        settlement = session.query(SettlementData).filter_by(
                            plant_id=gen_record.plant_id,
                            client_name=gen_record.client_name,
                            cons_unit=mapping.cons_unit,
                            date=gen_record.date,
                            time=gen_record.time,
                            type=gen_record.type
                        ).first()
                        
                        if settlement:
                            # Update generation allocation
                            settlement.allocated_generation = allocated_generation
                            
                            # Recalculate all settlement values
                            consumption = Decimal(settlement.consumption) if settlement.consumption is not None else Decimal(0)
                            deficit = allocated_generation - consumption
                            surplus_demand = max(Decimal(0), -deficit)
                            surplus_generation = max(Decimal(0), allocated_generation - consumption)
                            settled = min(allocated_generation, consumption)


                            
                            # Update all fields
                            settlement.deficit = deficit
                            settlement.surplus_demand = surplus_demand
                            settlement.surplus_generation = surplus_generation
                            settlement.settled = settled
                            
                            records_updated += 1
                
                except Exception as e:
                    logger.error(f"Error recalculating allocation for generation record: {e}")
                    continue
            
            # Commit changes
            session.commit()
            session.close()
            
            logger.info(f"✅ Updated generation allocation for {records_updated} records")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error recalculating generation allocation: {e}")
            import traceback
            traceback.print_exc()
            return False

# Example usage
def main():
    # Initialize the data inserter
    inserter = SettlementDataInserter()
    
    # Process settlement data for all records
    inserter.process_settlement_data()
    
    

if __name__ == "__main__":
    main()





# import pandas as pd
# from sqlalchemy import create_engine, select, func
# from sqlalchemy.orm import sessionmaker
# from models import TblGeneration, TblConsumption, ConsumptionMapping, SettlementData, Base
# from datetime import datetime, timedelta
# import logging
# from decimal import Decimal

# # Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# logger = logging.getLogger(__name__)

# class SettlementDataInserter:
#     def __init__(self, database_url="mysql+pymysql://root:test123@localhost/energy_db"):
#         """
#         Initialize the Settlement Data Inserter
        
#         Args:
#             database_url (str): Database connection URL
#         """
#         self.engine = create_engine(database_url)
#         self.Session = sessionmaker(bind=self.engine)
        
#     def process_settlement_data(self):
#         """
#         Process and insert settlement data from tbl_generation and tbl_consumption
#         using consumption_mapping to allocate generation
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Starting settlement data processing")
            
            
#             session = self.Session()
            
#             # Build generation query
#             gen_query = select(TblGeneration)
            
            
#             # Execute generation query
#             generation_records = session.execute(gen_query).scalars().all()
#             logger.info(f"Found {len(generation_records)} generation records")
            
#             # Process each generation record
#             records_added = 0
#             records_updated = 0
#             records_skipped = 0
            
#             for gen_record in generation_records:
#                 try:
#                     # Get consumption mapping for this client
#                     mapping_query = select(ConsumptionMapping).where(
#                         ConsumptionMapping.client_name == gen_record.client_name
#                     )
                    
#                     mappings = session.execute(mapping_query).scalars().all()
                    
#                     if not mappings:
#                         logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
#                         records_skipped += 1
#                         continue
                    
#                     # Process each mapping to allocate generation
#                     for mapping in mappings:
#                         # Calculate allocated generation based on percentage
#                         if gen_record.generation is not None:
#                             allocated_generation = Decimal(gen_record.generation) * (Decimal(mapping.percentage) / Decimal(100))
#                         else:
#                             allocated_generation = Decimal(0)
                        
#                         # Find matching consumption record
#                         cons_query = select(TblConsumption).where(
#                             TblConsumption.client_name == gen_record.client_name,
#                             TblConsumption.cons_unit == mapping.cons_unit,
#                             TblConsumption.date == gen_record.date,
#                             TblConsumption.time == gen_record.time
#                         )
                        
#                         cons_record = session.execute(cons_query).scalars().first()
                        
#                         # Set consumption value (0 if no matching record found)
#                         consumption = Decimal(cons_record.consumption) if cons_record and cons_record.consumption is not None else Decimal(0)
                        
#                         # Calculate deficit and surplus
#                         deficit = allocated_generation - consumption  # Positive when generation > consumption, negative when consumption > generation
#                         surplus_demand = max(Decimal(0), -deficit)    # Demand that wasn't met by allocated generation (when deficit is negative)
#                         surplus_generation = max(Decimal(0), allocated_generation - consumption)  # Generation that exceeds consumption
                        
#                         # Check if settlement record already exists
#                         existing = session.query(SettlementData).filter_by(
#                             plant_id=gen_record.plant_id,
#                             client_name=gen_record.client_name,
#                             cons_unit=mapping.cons_unit,
#                             date=gen_record.date,
#                             time=gen_record.time,
#                             type=gen_record.type
#                         ).first()
                        
#                         if existing:
#                             # Update existing record
#                             existing.allocated_generation = allocated_generation
#                             existing.consumption = consumption
#                             existing.deficit = deficit
#                             existing.surplus_demand = surplus_demand
#                             existing.surplus_generation = surplus_generation
#                             existing.settled = min(allocated_generation, consumption)
#                             records_updated += 1
#                         else:
#                             # Create new record
#                             settled = min(allocated_generation, consumption)
#                             new_record = SettlementData(
#                                 plant_id=gen_record.plant_id,
#                                 client_name=gen_record.client_name,
#                                 cons_unit=mapping.cons_unit,
#                                 type=gen_record.type,
#                                 date=gen_record.date,
#                                 time=gen_record.time,
#                                 allocated_generation=allocated_generation,
#                                 consumption=consumption,
#                                 deficit=deficit,
#                                 surplus_demand=surplus_demand,
#                                 surplus_generation=surplus_generation,
#                                 settled=settled
#                             )
#                             session.add(new_record)
#                             records_added += 1
                
#                 except Exception as e:
#                     logger.error(f"Error processing generation record: {e}")
#                     records_skipped += 1
#                     continue
            
#             # Process consumption records that don't have matching generation records
#             cons_query = select(TblConsumption)
            
            
#             consumption_records = session.execute(cons_query).scalars().all()
#             logger.info(f"Found {len(consumption_records)} consumption records")
            
#             for cons_record in consumption_records:
#                 try:
#                     # Check if there's already a settlement record for this consumption
#                     existing_settlement = session.query(SettlementData).filter(
#                         SettlementData.client_name == cons_record.client_name,
#                         SettlementData.cons_unit == cons_record.cons_unit,
#                         SettlementData.date == cons_record.date,
#                         SettlementData.time == cons_record.time
#                     ).first()
                    
#                     if not existing_settlement:
#                         # Find a mapping for this consumption unit
#                         mapping_query = select(ConsumptionMapping).where(
#                             ConsumptionMapping.client_name == cons_record.client_name,
#                             ConsumptionMapping.cons_unit == cons_record.cons_unit
#                         )
                        
#                         mapping = session.execute(mapping_query).scalars().first()
                        
#                         if not mapping:
#                             logger.warning(f"No mapping found for consumption unit: {cons_record.cons_unit}")
#                             records_skipped += 1
#                             continue
                        
#                         # Find a matching plant_id from generation records for this client
#                         plant_query = select(TblGeneration.plant_id, TblGeneration.type).where(
#                             TblGeneration.client_name == cons_record.client_name
#                         ).limit(1)
                        
#                         plant_result = session.execute(plant_query).first()
                        
#                         if plant_result:
#                             plant_id, plant_type = plant_result
                            
#                             # Create new settlement record with only consumption data
#                             consumption = Decimal(cons_record.consumption) if cons_record.consumption is not None else Decimal(0)
#                             # When no generation is allocated, deficit equals consumption, surplus_demand equals consumption
#                             deficit = consumption  # consumption - 0 (allocated_generation)
#                             surplus_demand = consumption  # All consumption is unmet demand
#                             new_record = SettlementData(
#                                 plant_id=plant_id,
#                                 client_name=cons_record.client_name,
#                                 cons_unit=cons_record.cons_unit,
#                                 type=plant_type,
#                                 date=cons_record.date,
#                                 time=cons_record.time,
#                                 allocated_generation=Decimal(0),
#                                 consumption=consumption,
#                                 deficit=deficit,
#                                 surplus_demand=surplus_demand,
#                                 surplus_generation=Decimal(0),
#                                 settled=Decimal(0)
#                             )
#                             session.add(new_record)
#                             records_added += 1
                
#                 except Exception as e:
#                     logger.error(f"Error processing consumption record: {e}")
#                     records_skipped += 1
#                     continue
            
#             # Commit changes
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Settlement data processing completed:")
#             logger.info(f"- {records_added} new records added")
#             logger.info(f"- {records_updated} records updated")
#             logger.info(f"- {records_skipped} records skipped")
            
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error processing settlement data: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

#     def recalculate_surplus_deficit(self, start_date=None, end_date=None, client_name=None):
#         """
#         Recalculate surplus/deficit for existing settlement data
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Recalculating surplus/deficit for settlement data")
            
#             session = self.Session()
            
#             # Build query
#             query = session.query(SettlementData)
            
#             if start_date:
#                 query = query.filter(SettlementData.date >= start_date)
#             if end_date:
#                 query = query.filter(SettlementData.date <= end_date)
#             if client_name:
#                 query = query.filter(SettlementData.client_name == client_name)
            
#             records = query.all()
#             updated_count = 0
            
#             for record in records:
#                 allocated_generation = Decimal(record.allocated_generation) if record.allocated_generation is not None else Decimal(0)
#                 consumption = Decimal(record.consumption) if record.consumption is not None else Decimal(0)
                
#                 # Calculate all settlement values
#                 deficit = allocated_generation - consumption
#                 surplus_demand = max(Decimal(0), -deficit)
#                 surplus_generation = max(Decimal(0), allocated_generation - consumption)
#                 settled = min(allocated_generation, consumption)
                
#                 # Update record
#                 record.deficit = deficit
#                 record.surplus_demand = surplus_demand
#                 record.surplus_generation = surplus_generation
#                 record.settled = settled
                
#                 updated_count += 1
            
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Updated surplus/deficit for {updated_count} records")
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error recalculating surplus/deficit: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

#     def recalculate_generation_allocation(self, start_date=None, end_date=None, client_name=None):
#         """
#         Recalculate generation allocation based on consumption_mapping percentages
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Recalculating generation allocation based on mapping percentages")
            
#             session = self.Session()
            
#             # Build generation query
#             gen_query = select(TblGeneration)
#             if start_date:
#                 gen_query = gen_query.where(TblGeneration.date >= start_date)
#             if end_date:
#                 gen_query = gen_query.where(TblGeneration.date <= end_date)
#             if client_name:
#                 gen_query = gen_query.where(TblGeneration.client_name == client_name)
            
#             # Execute generation query
#             generation_records = session.execute(gen_query).scalars().all()
#             logger.info(f"Found {len(generation_records)} generation records to reallocate")
            
#             records_updated = 0
            
#             for gen_record in generation_records:
#                 try:
#                     # Get consumption mapping for this client
#                     mapping_query = select(ConsumptionMapping).where(
#                         ConsumptionMapping.client_name == gen_record.client_name
#                     )
                    
#                     mappings = session.execute(mapping_query).scalars().all()
                    
#                     if not mappings:
#                         logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
#                         continue
                    
#                     # Process each mapping to allocate generation
#                     for mapping in mappings:
#                         # Calculate allocated generation based on percentage
#                         if gen_record.generation is not None:
#                             allocated_generation = Decimal(gen_record.generation) * (Decimal(mapping.percentage) / Decimal(100))
#                         else:
#                             allocated_generation = Decimal(0)
                        
#                         # Find settlement record
#                         settlement = session.query(SettlementData).filter_by(
#                             plant_id=gen_record.plant_id,
#                             client_name=gen_record.client_name,
#                             cons_unit=mapping.cons_unit,
#                             date=gen_record.date,
#                             time=gen_record.time,
#                             type=gen_record.type
#                         ).first()
                        
#                         if settlement:
#                             # Update generation allocation
#                             settlement.allocated_generation = allocated_generation
                            
#                             # Recalculate all settlement values
#                             consumption = Decimal(settlement.consumption) if settlement.consumption is not None else Decimal(0)
#                             deficit = allocated_generation - consumption
#                             surplus_demand = max(Decimal(0), -deficit)
#                             surplus_generation = max(Decimal(0), allocated_generation - consumption)
#                             settled = min(allocated_generation, consumption)


                            
#                             # Update all fields
#                             settlement.deficit = deficit
#                             settlement.surplus_demand = surplus_demand
#                             settlement.surplus_generation = surplus_generation
#                             settlement.settled = settled
                            
#                             records_updated += 1
                
#                 except Exception as e:
#                     logger.error(f"Error recalculating allocation for generation record: {e}")
#                     continue
            
#             # Commit changes
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Updated generation allocation for {records_updated} records")
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error recalculating generation allocation: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

# # Example usage
# def main():
#     # Initialize the data inserter
#     inserter = SettlementDataInserter()
    
#     # Process settlement data for all records
#     inserter.process_settlement_data()
    
    

# if __name__ == "__main__":
#     main()