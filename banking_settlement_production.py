"""
Karnataka 2025 Banking Settlement Processor - Production Version

This module processes monthly banking settlement following Karnataka 2025 policy
with proper intra-TOD (2% charges) and inter-TOD (8% charges) settlement logic.

Author: System
Version: 1.0.0
"""

from sqlalchemy import func, and_
from sqlalchemy.orm import Session
from datetime import time
from decimal import Decimal
import logging
from typing import Dict, List, Tuple, Optional
from models import SettlementData, BankingSettlement, TblPlants

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Karnataka 2025 Policy Configuration
KARNATAKA_INTRA_TOD_CHARGE_RATE = Decimal('0.02')  # 2% charges for intra-TOD settlement
KARNATAKA_INTER_TOD_CHARGE_RATE = Decimal('0.08')  # 8% charges for inter-TOD settlement

# Time-of-Day slots according to Karnataka 2025 policy
KARNATAKA_TIME_SLOTS = {
    'Morning Peak': {
        'start_time': time(6, 0),   # 06:00 hours
        'end_time': time(9, 0),     # 09:00 hours
        'slot_time_display': '6am to 9am'
    },
    'Day (Normal)': {
        'start_time': time(9, 0),   # 09:00 hours
        'end_time': time(18, 0),    # 18:00 hours
        'slot_time_display': '9am to 6pm'
    },
    'Evening Peak': {
        'start_time': time(18, 0),  # 18:00 hours
        'end_time': time(22, 0),    # 22:00 hours
        'slot_time_display': '6pm to 10pm'
    },
    'Night Off-Peak': {
        'start_time': time(22, 0),  # 22:00 hours
        'end_time': time(6, 0),     # 06:00 hours (next day)
        'slot_time_display': '10pm to 6am'
    }
}


class KarnatakaBankingSettlementProcessor:
    """
    Processes monthly banking settlement according to Karnataka 2025 policy.
    
    This processor handles:
    - Data aggregation by time slots
    - Intra-TOD settlement (within same slot, 2% charges)
    - Inter-TOD settlement (financial settlement, 8% charges)
    - Comprehensive reporting and analytics
    """
    
    def __init__(self, database_session: Session):
        """
        Initialize the processor with database session.
        
        Args:
            database_session: SQLAlchemy session for database operations
        """
        self.db_session = database_session
        self._validate_session()
    
    def _validate_session(self) -> None:
        """Validate that database session is properly configured."""
        if not self.db_session:
            raise ValueError("Database session is required")
    
    def process_monthly_settlement(self, settlement_year: int, settlement_month: int) -> bool:
        """
        Main method to process monthly banking settlement following Karnataka 2025 policy.
        
        Args:
            settlement_year: Year for settlement processing (e.g., 2024)
            settlement_month: Month for settlement processing (1-12)
        
        Returns:
            bool: True if settlement processed successfully, False otherwise
        """
        try:
            settlement_date_string = f"{settlement_year:04d}-{settlement_month:02d}"
            logger.info(f"Starting Karnataka 2025 banking settlement for {settlement_date_string}")
            
            # Step 1: Clear existing banking data for this month
            self._clear_existing_settlement_data(settlement_date_string)
            
            # Step 2: Aggregate settlement data by time slots
            aggregated_settlement_data = self._aggregate_monthly_settlement_data(settlement_year, settlement_month)
            
            if not aggregated_settlement_data:
                logger.warning(f"No settlement data found for {settlement_date_string}")
                return False
            
            # Step 3: Create initial banking records
            self._create_initial_banking_records(aggregated_settlement_data, settlement_date_string)
            
            # Step 4: Process intra-TOD settlement (within same slot, 2% charges)
            self._process_intra_tod_settlement(settlement_date_string)
            
            # Step 5: Process inter-TOD settlement (financial settlement, 8% charges)
            self._process_inter_tod_settlement(settlement_date_string)
            
            # Commit all changes
            self.db_session.commit()
            logger.info(f"Karnataka 2025 banking settlement completed successfully for {settlement_date_string}")
            return True
            
        except Exception as settlement_error:
            logger.error(f"Error in monthly banking settlement: {str(settlement_error)}")
            self.db_session.rollback()
            return False
    
    def _clear_existing_settlement_data(self, settlement_date_string: str) -> None:
        """
        Clear existing banking settlement data for the given month.
        
        Args:
            settlement_date_string: Date string in format YYYY-MM
        """
        try:
            deleted_records_count = self.db_session.query(BankingSettlement).filter(
                BankingSettlement.date == settlement_date_string
            ).delete(synchronize_session=False)
            
            self.db_session.flush()
            logger.info(f"Cleared {deleted_records_count} existing banking records for {settlement_date_string}")
            
        except Exception as clear_error:
            logger.error(f"Error clearing existing settlement data: {str(clear_error)}")
            raise
    
    def _determine_time_slot_from_time(self, time_object: time) -> Tuple[str, str]:
        """
        Determine slot name and display string from a time object.
        
        Args:
            time_object: Time object to categorize into TOD slots
            
        Returns:
            Tuple of (slot_name, slot_time_display)
        """
        for slot_name, slot_configuration in KARNATAKA_TIME_SLOTS.items():
            slot_start_time = slot_configuration['start_time']
            slot_end_time = slot_configuration['end_time']
            
            # Handle overnight slot (Night Off-Peak: 22:00 to 06:00)
            if slot_start_time > slot_end_time:  # Crosses midnight
                if time_object >= slot_start_time or time_object < slot_end_time:
                    return slot_name, slot_configuration['slot_time_display']
            else:  # Normal slot within same day
                if slot_start_time <= time_object < slot_end_time:
                    return slot_name, slot_configuration['slot_time_display']
        
        # Fallback for edge cases
        logger.warning(f"Unable to categorize time {time_object}, using default slot")
        return 'Unknown', '00:00-00:00'
    
    def _aggregate_monthly_settlement_data(self, settlement_year: int, settlement_month: int) -> List[Dict]:
        """
        Aggregate settlement data by client, plant, consumption unit, type, and time slot.
        
        Args:
            settlement_year: Year for data aggregation
            settlement_month: Month for data aggregation
        
        Returns:
            List of dictionaries containing aggregated settlement data
        """
        logger.info(f"Aggregating settlement data for {settlement_year}-{settlement_month:02d}")
        
        # Retrieve all settlement records for the specified month
        monthly_settlement_records = self.db_session.query(SettlementData).filter(
            and_(
                func.year(SettlementData.date) == settlement_year,
                func.month(SettlementData.date) == settlement_month
            )
        ).all()
        
        logger.info(f"Found {len(monthly_settlement_records)} settlement records for processing")
        
        # Group data by unique business keys and determine time slots
        grouped_settlement_data = {}
        
        for settlement_record in monthly_settlement_records:
            # Determine time slot from the record's time
            time_slot_name, time_slot_display = self._determine_time_slot_from_time(settlement_record.time)
            
            # Get plant name for the record
            plant_name = self._get_plant_name_by_id(settlement_record.plant_id)
            
            # Create unique grouping key
            settlement_grouping_key = (
                settlement_record.client_name,
                plant_name,
                settlement_record.cons_unit,
                time_slot_name,
                settlement_record.type
            )
            
            if settlement_grouping_key not in grouped_settlement_data:
                grouped_settlement_data[settlement_grouping_key] = {
                    'client_name': settlement_record.client_name,
                    'plant_id': settlement_record.plant_id,
                    'plant_name': plant_name,
                    'cons_unit': settlement_record.cons_unit,
                    'type': settlement_record.type,
                    'slot_name': time_slot_name,
                    'slot_time_display': time_slot_display,
                    'total_surplus_demand': Decimal('0.00'),
                    'total_surplus_generation': Decimal('0.00'),
                    'total_matched_settled': Decimal('0.00')
                }
            
            # Aggregate monetary values
            grouped_settlement_data[settlement_grouping_key]['total_surplus_demand'] += (
                settlement_record.surplus_demand or Decimal('0.00')
            )
            grouped_settlement_data[settlement_grouping_key]['total_surplus_generation'] += (
                settlement_record.surplus_generation or Decimal('0.00')
            )
            grouped_settlement_data[settlement_grouping_key]['total_matched_settled'] += (
                settlement_record.settled or Decimal('0.00')
            )
        
        # Convert grouped data to list format
        aggregated_data_list = list(grouped_settlement_data.values())
        
        logger.info(f"Created {len(aggregated_data_list)} aggregated settlement records")
        
        # Log sample record for verification
        if aggregated_data_list:
            sample_record = aggregated_data_list[0]
            logger.info(
                f"Sample aggregated record: Client={sample_record['client_name']}, "
                f"Plant={sample_record['plant_name']}, "
                f"Unit={sample_record['cons_unit']}, "
                f"Slot={sample_record['slot_name']}"
            )
        
        return aggregated_data_list
    
    def _get_plant_name_by_id(self, plant_id: str) -> str:
        """
        Retrieve plant name from plant ID.
        
        Args:
            plant_id: Unique plant identifier
            
        Returns:
            Plant name if found, otherwise plant_id as fallback
        """
        try:
            plant_record = self.db_session.query(TblPlants).filter(
                TblPlants.plant_id == plant_id
            ).first()
            return plant_record.plant_name if plant_record else plant_id
        except Exception as plant_query_error:
            logger.warning(f"Error querying plant name for ID {plant_id}: {str(plant_query_error)}")
            return plant_id
    
    def _create_initial_banking_records(self, aggregated_data_list: List[Dict], settlement_date_string: str) -> None:
        """
        Create initial banking settlement records from aggregated data.
        
        Args:
            aggregated_data_list: List of aggregated settlement data
            settlement_date_string: Settlement date in YYYY-MM format
        """
        logger.info(f"Creating {len(aggregated_data_list)} initial banking records")
        
        # Deduplicate records based on unique business constraints
        unique_record_keys = set()
        deduplicated_data_list = []
        
        for data_record in aggregated_data_list:
            # Create unique key based on database constraints
            unique_constraint_key = (
                data_record['client_name'],
                data_record['plant_name'],
                data_record['cons_unit'],
                data_record['slot_name'],
                settlement_date_string
            )
            
            if unique_constraint_key in unique_record_keys:
                logger.warning(f"Duplicate record detected and will be merged: {unique_constraint_key}")
                # Find existing record and merge values
                existing_record = next(
                    (record for record in deduplicated_data_list 
                     if (record['client_name'], record['plant_name'], 
                         record['cons_unit'], record['slot_name']) == unique_constraint_key[:-1]),
                    None
                )
                if existing_record:
                    existing_record['total_surplus_demand'] += data_record['total_surplus_demand']
                    existing_record['total_surplus_generation'] += data_record['total_surplus_generation']
                    existing_record['total_matched_settled'] += data_record['total_matched_settled']
                    logger.info(f"Merged duplicate record for {unique_constraint_key}")
            else:
                unique_record_keys.add(unique_constraint_key)
                deduplicated_data_list.append(data_record)
        
        logger.info(f"After deduplication: {len(deduplicated_data_list)} records will be inserted")
        
        # Create BankingSettlement objects
        banking_settlement_records = []
        for data_record in deduplicated_data_list:
            banking_record = BankingSettlement(
                client_name=data_record['client_name'],
                plant_name=data_record['plant_name'],
                date=settlement_date_string,
                type=data_record['type'],
                cons_unit=data_record['cons_unit'],
                slot_name=data_record['slot_name'],
                slot_time=data_record['slot_time_display'],
                surplus_demand_sum=data_record['total_surplus_demand'],
                surplus_generation_sum=data_record['total_surplus_generation'],
                matched_settled_sum=data_record['total_matched_settled'],
                # Initialize post-settlement values
                surplus_generation_sum_after_intra=data_record['total_surplus_generation'],
                surplus_demand_sum_after_intra=data_record['total_surplus_demand'],
                intra_settlement=Decimal('0.00'),
                surplus_generation_sum_after_inter=data_record['total_surplus_generation'],
                surplus_demand_sum_after_inter=data_record['total_surplus_demand'],
                inter_settlement=Decimal('0.00')
            )
            banking_settlement_records.append(banking_record)
        
        try:
            self.db_session.add_all(banking_settlement_records)
            self.db_session.flush()  # Ensure records are written and constraint violations are caught
            logger.info("Initial banking settlement records created successfully")
        except Exception as record_creation_error:
            logger.error(f"Error creating banking settlement records: {str(record_creation_error)}")
            raise
    
    def _process_intra_tod_settlement(self, settlement_date_string: str) -> None:
        """
        Process intra-TOD settlement: Match surplus generation and demand within same time slot.
        Karnataka 2025 policy applies 2% charges for intra-slot settlements.
        
        Args:
            settlement_date_string: Settlement date in YYYY-MM format
        """
        logger.info(f"Processing intra-TOD settlement with {KARNATAKA_INTRA_TOD_CHARGE_RATE*100}% charges")
        
        # Get all unique time slots for this settlement month
        unique_slots_query = self.db_session.query(BankingSettlement.slot_name).filter(
            BankingSettlement.date == settlement_date_string
        ).distinct().all()
        
        total_intra_settlement_amount = Decimal('0.00')
        
        for slot_tuple in unique_slots_query:
            slot_name = slot_tuple[0]
            slot_settlement_amount = self._process_single_slot_intra_settlement(settlement_date_string, slot_name)
            total_intra_settlement_amount += slot_settlement_amount
        
        logger.info(f"Total intra-TOD settlement amount: {total_intra_settlement_amount}")
    
    def _process_single_slot_intra_settlement(self, settlement_date_string: str, slot_name: str) -> Decimal:
        """
        Process intra-TOD settlement for a specific time slot.
        
        Args:
            settlement_date_string: Settlement date in YYYY-MM format
            slot_name: Name of the time slot to process
            
        Returns:
            Total settlement amount for the slot
        """
        # Get all banking records for this specific slot
        slot_banking_records = self.db_session.query(BankingSettlement).filter(
            and_(
                BankingSettlement.date == settlement_date_string,
                BankingSettlement.slot_name == slot_name
            )
        ).all()
        
        total_slot_settlement = Decimal('0.00')
        
        # Process each record using Karnataka 2025 intra-TOD settlement logic
        for banking_record in slot_banking_records:
            surplus_demand = banking_record.surplus_demand_sum
            surplus_generation = banking_record.surplus_generation_sum
            
            # Apply Karnataka intra-TOD settlement calculation
            (demand_after_intra, 
             generation_after_intra, 
             intra_settlement_amount) = self._calculate_karnataka_intra_settlement(
                surplus_demand, surplus_generation, KARNATAKA_INTRA_TOD_CHARGE_RATE
            )
            
            # Update banking record with settlement results
            banking_record.surplus_demand_sum_after_intra = demand_after_intra
            banking_record.surplus_generation_sum_after_intra = generation_after_intra
            banking_record.intra_settlement = intra_settlement_amount
            
            total_slot_settlement += intra_settlement_amount
            
            if intra_settlement_amount > Decimal('0.00'):
                logger.debug(
                    f"Intra settlement - Slot: {slot_name}, Client: {banking_record.client_name}, "
                    f"Original demand: {surplus_demand}, Original generation: {surplus_generation}, "
                    f"Post-settlement demand: {demand_after_intra}, "
                    f"Post-settlement generation: {generation_after_intra}, "
                    f"Settlement amount: {intra_settlement_amount}"
                )
        
        return total_slot_settlement
    
    def _calculate_karnataka_intra_settlement(
        self, 
        surplus_demand: Decimal, 
        surplus_generation: Decimal, 
        intra_charge_rate: Decimal
    ) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Calculate intra-TOD settlement following Karnataka 2025 policy.
        
        Special Case Handling:
        - Generation=0, Demand>0: Skip settlement, pass demand to inter-TOD stage
        - Generation>0, Demand=0: Skip settlement, pass generation unchanged
        - Both>0: Apply normal intra-TOD settlement logic with charges
        
        Args:
            surplus_demand: Original surplus demand amount
            surplus_generation: Original surplus generation amount
            intra_charge_rate: Intra-TOD charge rate (e.g., 0.02 for 2%)
            
        Returns:
            Tuple of (demand_after_intra, generation_after_intra, intra_settlement_amount)
        """
        
        # Special Case 1: No generation available, demand exists
        if surplus_generation == Decimal('0.00') and surplus_demand > Decimal('0.00'):
            # Pass demand unchanged to inter-TOD settlement
            return surplus_demand, Decimal('0.00'), Decimal('0.00')
        
        # Special Case 2: Generation available, no demand
        elif surplus_generation > Decimal('0.00') and surplus_demand == Decimal('0.00'):
            # Pass generation unchanged (no demand to settle)
            return Decimal('0.00'), surplus_generation, Decimal('0.00')
        
        # Normal Case: Both demand and generation exist
        elif surplus_generation > Decimal('0.00') and surplus_demand > Decimal('0.00'):
            # Calculate required generation including charges
            required_generation_with_charges = surplus_demand * (Decimal('1') + intra_charge_rate)
            
            if surplus_generation >= required_generation_with_charges:
                # Full settlement possible - all demand can be satisfied
                demand_after_settlement = Decimal('0.00')
                generation_after_settlement = surplus_generation - required_generation_with_charges
                settlement_amount = required_generation_with_charges
                
            else:
                # Partial settlement - limited by available generation
                settled_demand_amount = surplus_generation / (Decimal('1') + intra_charge_rate)
                demand_after_settlement = surplus_demand - settled_demand_amount
                generation_after_settlement = Decimal('0.00')
                settlement_amount = surplus_generation
                
            return demand_after_settlement, generation_after_settlement, settlement_amount
        
        else:
            # Both values are zero - no settlement needed
            return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')
    
    def _process_inter_tod_settlement(self, settlement_date_string: str) -> None:
        """
        Process inter-TOD settlement: Apply financial settlement to clear remaining demand.
        Karnataka 2025 policy applies 8% penalty for inter-TOD financial settlements.
        
        Args:
            settlement_date_string: Settlement date in YYYY-MM format
        """
        logger.info(f"Processing inter-TOD settlement with {KARNATAKA_INTER_TOD_CHARGE_RATE*100}% charges")
        
        # Get all banking records for this settlement month
        monthly_banking_records = self.db_session.query(BankingSettlement).filter(
            BankingSettlement.date == settlement_date_string
        ).all()
        
        total_inter_settlement_amount = Decimal('0.00')
        
        # Process each record individually for inter-TOD settlement
        for banking_record in monthly_banking_records:
            demand_after_intra = banking_record.surplus_demand_sum_after_intra
            generation_after_intra = banking_record.surplus_generation_sum_after_intra
            
            # Apply inter-TOD settlement calculation
            (demand_after_inter, 
             generation_after_inter, 
             inter_settlement_amount) = self._calculate_karnataka_inter_settlement(
                demand_after_intra, generation_after_intra, KARNATAKA_INTER_TOD_CHARGE_RATE
            )
            
            # Update banking record with final settlement results
            banking_record.surplus_demand_sum_after_inter = demand_after_inter
            banking_record.surplus_generation_sum_after_inter = generation_after_inter
            banking_record.inter_settlement = inter_settlement_amount
            
            total_inter_settlement_amount += inter_settlement_amount
            
            if inter_settlement_amount > Decimal('0.00'):
                logger.debug(
                    f"Inter settlement - Client: {banking_record.client_name}, "
                    f"Slot: {banking_record.slot_name}, "
                    f"Demand after intra: {demand_after_intra}, "
                    f"Generation after intra: {generation_after_intra}, "
                    f"Final demand: {demand_after_inter}, "
                    f"Final generation: {generation_after_inter}, "
                    f"Inter settlement amount: {inter_settlement_amount}"
                )
        
        logger.info(f"Total inter-TOD settlement amount: {total_inter_settlement_amount}")
    
    def _calculate_karnataka_inter_settlement(
        self, 
        demand_after_intra: Decimal, 
        generation_after_intra: Decimal, 
        inter_charge_rate: Decimal
    ) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Calculate inter-TOD settlement following Karnataka 2025 policy.
        
        Inter-TOD settlement applies financial settlement to clear remaining demand
        with penalty charges. Generation values remain unchanged as this is
        purely a financial settlement mechanism.
        
        Args:
            demand_after_intra: Remaining demand after intra-TOD settlement
            generation_after_intra: Remaining generation after intra-TOD settlement
            inter_charge_rate: Inter-TOD charge rate (e.g., 0.08 for 8%)
            
        Returns:
            Tuple of (demand_after_inter, generation_after_inter, inter_settlement_amount)
        """
        
        # Generation remains unchanged in inter-TOD settlement (financial only)
        final_generation = generation_after_intra
        
        # Apply inter-TOD settlement for remaining demand
        if demand_after_intra > Decimal('0.00'):
            # Calculate settlement amount using Karnataka formula
            remaining_demand = demand_after_intra
            inter_settlement_amount = remaining_demand / (Decimal('1') + inter_charge_rate)
            
            # Calculate final demand after financial settlement
            final_demand = remaining_demand - inter_settlement_amount
            
            return final_demand, final_generation, inter_settlement_amount
        else:
            # No remaining demand to settle
            return Decimal('0.00'), final_generation, Decimal('0.00')
    
    def generate_settlement_summary(self, settlement_year: int, settlement_month: int) -> Dict:
        """
        Generate comprehensive settlement summary for a given month.
        
        Args:
            settlement_year: Year for summary generation
            settlement_month: Month for summary generation
            
        Returns:
            Dictionary containing detailed settlement summary
        """
        settlement_date_string = f"{settlement_year:04d}-{settlement_month:02d}"
        
        # Generate slot-wise summary
        slot_wise_summary = {}
        for slot_name, slot_config in KARNATAKA_TIME_SLOTS.items():
            slot_summary_data = self.db_session.query(
                func.sum(BankingSettlement.surplus_demand_sum).label('original_demand'),
                func.sum(BankingSettlement.surplus_generation_sum).label('original_generation'),
                func.sum(BankingSettlement.surplus_demand_sum_after_intra).label('demand_after_intra'),
                func.sum(BankingSettlement.surplus_generation_sum_after_intra).label('generation_after_intra'),
                func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('final_demand'),
                func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('final_generation'),
                func.sum(BankingSettlement.intra_settlement).label('total_intra_settlement'),
                func.sum(BankingSettlement.inter_settlement).label('total_inter_settlement'),
                func.count().label('record_count')
            ).filter(
                and_(
                    BankingSettlement.date == settlement_date_string,
                    BankingSettlement.slot_name == slot_name
                )
            ).first()
            
            slot_wise_summary[slot_name] = {
                'slot_time_display': slot_config['slot_time_display'],
                'original_surplus_demand': slot_summary_data.original_demand or Decimal('0.00'),
                'original_surplus_generation': slot_summary_data.original_generation or Decimal('0.00'),
                'demand_after_intra_settlement': slot_summary_data.demand_after_intra or Decimal('0.00'),
                'generation_after_intra_settlement': slot_summary_data.generation_after_intra or Decimal('0.00'),
                'final_grid_consumption': slot_summary_data.final_demand or Decimal('0.00'),
                'final_surplus_generation': slot_summary_data.final_generation or Decimal('0.00'),
                'total_intra_settlement': slot_summary_data.total_intra_settlement or Decimal('0.00'),
                'total_inter_settlement': slot_summary_data.total_inter_settlement or Decimal('0.00'),
                'record_count': slot_summary_data.record_count or 0
            }
        
        # Generate overall monthly summary
        overall_summary_data = self.db_session.query(
            func.sum(BankingSettlement.surplus_demand_sum).label('total_consumption'),
            func.sum(BankingSettlement.surplus_generation_sum).label('total_generation'),
            func.sum(BankingSettlement.surplus_demand_sum_after_inter).label('grid_consumption'),
            func.sum(BankingSettlement.surplus_generation_sum_after_inter).label('surplus_generation'),
            func.sum(BankingSettlement.intra_settlement).label('total_intra_settlement'),
            func.sum(BankingSettlement.inter_settlement).label('total_inter_settlement')
        ).filter(
            BankingSettlement.date == settlement_date_string
        ).first()
        
        total_consumption = overall_summary_data.total_consumption or Decimal('0.00')
        grid_consumption = overall_summary_data.grid_consumption or Decimal('0.00')
        renewable_replacement = total_consumption - grid_consumption
        
        # Calculate efficiency metrics
        replacement_percentage = float(renewable_replacement / total_consumption * 100) if total_consumption > 0 else 0.0
        
        return {
            'settlement_month': settlement_month,
            'settlement_year': settlement_year,
            'settlement_date': settlement_date_string,
            'slot_wise_summary': slot_wise_summary,
            'overall_monthly_summary': {
                'total_consumption': total_consumption,
                'grid_consumption': grid_consumption,
                'renewable_replacement': renewable_replacement,
                'replacement_percentage': round(replacement_percentage, 2),
                'final_surplus_generation': overall_summary_data.surplus_generation or Decimal('0.00'),
                'total_intra_settlement': overall_summary_data.total_intra_settlement or Decimal('0.00'),
                'total_inter_settlement': overall_summary_data.total_inter_settlement or Decimal('0.00')
            },
            'policy_configuration': {
                'policy_name': 'Karnataka_2025',
                'intra_tod_charge_percentage': float(KARNATAKA_INTRA_TOD_CHARGE_RATE * 100),
                'inter_tod_charge_percentage': float(KARNATAKA_INTER_TOD_CHARGE_RATE * 100)
            },
            'time_slot_definitions': KARNATAKA_TIME_SLOTS
        }
    
    def get_available_settlement_months(self) -> List[Tuple[int, int]]:
        """
        Get all available year-month combinations from settlement data.
        
        Returns:
            List of (year, month) tuples available for settlement processing
        """
        available_months_query = self.db_session.query(
            func.year(SettlementData.date).label('year'),
            func.month(SettlementData.date).label('month')
        ).distinct().order_by('year', 'month').all()
        
        return [(year, month) for year, month in available_months_query]
    
    def process_all_available_months(self) -> Dict:
        """
        Process banking settlement for all available months in the database.
        
        Returns:
            Dictionary containing processing results and statistics
        """
        available_months = self.get_available_settlement_months()
        
        processing_results = {
            'total_months_found': len(available_months),
            'successfully_processed': 0,
            'failed_months': [],
            'monthly_processing_details': [],
            'overall_success_rate': 0.0
        }
        
        logger.info(f"Starting batch processing for {len(available_months)} available months")
        
        for settlement_year, settlement_month in available_months:
            try:
                logger.info(f"Processing settlement for {settlement_year}-{settlement_month:02d}")
                
                processing_success = self.process_monthly_settlement(settlement_year, settlement_month)
                
                if processing_success:
                    processing_results['successfully_processed'] += 1
                    monthly_summary = self.generate_settlement_summary(settlement_year, settlement_month)
                    
                    processing_results['monthly_processing_details'].append({
                        'year': settlement_year,
                        'month': settlement_month,
                        'status': 'SUCCESS',
                        'summary': monthly_summary
                    })
                    logger.info(f"Successfully processed {settlement_year}-{settlement_month:02d}")
                else:
                    processing_results['failed_months'].append((settlement_year, settlement_month))
                    processing_results['monthly_processing_details'].append({
                        'year': settlement_year,
                        'month': settlement_month,
                        'status': 'FAILED',
                        'error': 'Processing returned False'
                    })
                    logger.error(f"Failed to process {settlement_year}-{settlement_month:02d}")
                    
            except Exception as processing_error:
                processing_results['failed_months'].append((settlement_year, settlement_month))
                processing_results['monthly_processing_details'].append({
                    'year': settlement_year,
                    'month': settlement_month,
                    'status': 'ERROR',
                    'error': str(processing_error)
                })
                logger.error(f"Error processing {settlement_year}-{settlement_month:02d}: {str(processing_error)}")
        
        # Calculate success rate
        if processing_results['total_months_found'] > 0:
            processing_results['overall_success_rate'] = round(
                (processing_results['successfully_processed'] / processing_results['total_months_found'] * 100), 2
            )
        
        logger.info(
            f"Batch processing completed: {processing_results['successfully_processed']}/{processing_results['total_months_found']} "
            f"months processed successfully ({processing_results['overall_success_rate']}% success rate)"
        )
        
        return processing_results


def create_karnataka_processor(database_session: Session) -> KarnatakaBankingSettlementProcessor:
    """
    Factory function to create a Karnataka banking settlement processor.
    
    Args:
        database_session: SQLAlchemy database session
        
    Returns:
        Configured KarnatakaBankingSettlementProcessor instance
    """
    return KarnatakaBankingSettlementProcessor(database_session)


# Example usage function for testing/demonstration
def demonstrate_karnataka_settlement(database_session: Session, target_year: int, target_month: int) -> Dict:
    """
    Demonstrate Karnataka settlement processing for a specific month.
    
    Args:
        database_session: Database session for processing
        target_year: Year to process
        target_month: Month to process
        
    Returns:
        Settlement summary dictionary
    """
    processor = create_karnataka_processor(database_session)
    
    # Process the settlement
    processing_success = processor.process_monthly_settlement(target_year, target_month)
    
    if not processing_success:
        raise Exception(f"Failed to process settlement for {target_year}-{target_month:02d}")
    
    # Generate and return summary
    settlement_summary = processor.generate_settlement_summary(target_year, target_month)
    
    # Log key metrics
    overall = settlement_summary['overall_monthly_summary']
    logger.info(f"Karnataka 2025 Settlement Summary for {target_year}-{target_month:02d}:")
    logger.info(f"  Total Consumption: {overall['total_consumption']}")
    logger.info(f"  Grid Consumption: {overall['grid_consumption']}")
    logger.info(f"  Renewable Replacement: {overall['renewable_replacement']} ({overall['replacement_percentage']}%)")
    logger.info(f"  Intra-TOD Settlement: {overall['total_intra_settlement']}")
    logger.info(f"  Inter-TOD Settlement: {overall['total_inter_settlement']}")
    
    return settlement_summary


def main():
    """
    Main function to run Karnataka 2025 Banking Settlement Processing.
    
    This function demonstrates different ways to use the settlement processor:
    1. Process a specific month
    2. Process all available months
    3. Generate detailed reports
    """
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    # Database configuration - Update these values for your environment
    DATABASE_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'username': 'root',
        'password': 'test123',
        'database': 'energy_db'
    }
    
    # Create database connection
    database_url = f"mysql+pymysql://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
    
    try:
        # Initialize database connection
        engine = create_engine(database_url, echo=False)
        SessionLocal = sessionmaker(bind=engine)
        db_session = SessionLocal()
        
        logger.info("=" * 80)
        logger.info("KARNATAKA 2025 BANKING SETTLEMENT PROCESSOR - PRODUCTION VERSION")
        logger.info("=" * 80)
        
        # Create processor instance
        settlement_processor = create_karnataka_processor(db_session)
        
        # Get available months for processing
        available_months = settlement_processor.get_available_settlement_months()
        
        if not available_months:
            logger.error("No settlement data found in database")
            return
        
        logger.info(f"Found {len(available_months)} months available for processing:")
        for year, month in available_months:
            logger.info(f"  - {year}-{month:02d}")
        
        print("\nChoose processing option:")
        print("1. Process a specific month")
        print("2. Process all available months")
        print("3. Generate report for existing processed month")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            # Process specific month
            print("\nAvailable months:")
            for i, (year, month) in enumerate(available_months, 1):
                print(f"{i}. {year}-{month:02d}")
            
            try:
                month_choice = int(input(f"\nSelect month (1-{len(available_months)}): "))
                if 1 <= month_choice <= len(available_months):
                    target_year, target_month = available_months[month_choice - 1]
                    
                    logger.info(f"\nProcessing settlement for {target_year}-{target_month:02d}...")
                    summary = demonstrate_karnataka_settlement(db_session, target_year, target_month)
                    
                    # Display detailed results
                    display_settlement_summary(summary)
                    
                else:
                    logger.error("Invalid selection")
            except ValueError:
                logger.error("Invalid input. Please enter a number.")
        
        elif choice == "2":
            # Process all available months
            logger.info("\nProcessing all available months...")
            processing_results = settlement_processor.process_all_available_months()
            
            # Display batch processing results
            display_batch_processing_results(processing_results)
        
        elif choice == "3":
            # Generate report for existing processed month
            print("\nAvailable months:")
            for i, (year, month) in enumerate(available_months, 1):
                print(f"{i}. {year}-{month:02d}")
            
            try:
                month_choice = int(input(f"\nSelect month for report (1-{len(available_months)}): "))
                if 1 <= month_choice <= len(available_months):
                    target_year, target_month = available_months[month_choice - 1]
                    
                    logger.info(f"\nGenerating report for {target_year}-{target_month:02d}...")
                    summary = settlement_processor.generate_settlement_summary(target_year, target_month)
                    
                    # Display detailed results
                    display_settlement_summary(summary)
                    
                else:
                    logger.error("Invalid selection")
            except ValueError:
                logger.error("Invalid input. Please enter a number.")
        
        elif choice == "4":
            logger.info("Exiting...")
            
        else:
            logger.error("Invalid choice. Please select 1-4.")
    
    except Exception as main_error:
        logger.error(f"Critical error in main execution: {str(main_error)}")
        if 'db_session' in locals():
            db_session.rollback()
    
    finally:
        if 'db_session' in locals():
            db_session.close()
            logger.info("Database connection closed")


def display_settlement_summary(summary: Dict) -> None:
    """
    Display formatted settlement summary.
    
    Args:
        summary: Settlement summary dictionary
    """
    print("\n" + "=" * 100)
    print(f"KARNATAKA 2025 SETTLEMENT SUMMARY - {summary['settlement_year']}-{summary['settlement_month']:02d}")
    print("=" * 100)
    
    # Overall summary
    overall = summary['overall_monthly_summary']
    print(f"\n📊 OVERALL MONTHLY SUMMARY:")
    print(f"   Total Consumption:        {overall['total_consumption']:>15}")
    print(f"   Grid Consumption:         {overall['grid_consumption']:>15}")
    print(f"   Renewable Replacement:    {overall['renewable_replacement']:>15} ({overall['replacement_percentage']}%)")
    print(f"   Final Surplus Generation: {overall['final_surplus_generation']:>15}")
    print(f"   Total Intra-TOD Settlement: {overall['total_intra_settlement']:>13}")
    print(f"   Total Inter-TOD Settlement: {overall['total_inter_settlement']:>13}")
    
    # Slot-wise breakdown
    print(f"\n🕐 SLOT-WISE BREAKDOWN:")
    print("-" * 100)
    
    for slot_name, slot_data in summary['slot_wise_summary'].items():
        if slot_data['record_count'] > 0:  # Only show slots with data
            print(f"\n{slot_name} ({slot_data['slot_time_display']}):")
            print(f"   Records Processed:        {slot_data['record_count']:>15}")
            print(f"   Original Surplus Demand:  {slot_data['original_surplus_demand']:>15}")
            print(f"   Original Surplus Generation: {slot_data['original_surplus_generation']:>12}")
            print(f"   After Intra Settlement:")
            print(f"     Demand:                 {slot_data['demand_after_intra_settlement']:>15}")
            print(f"     Generation:             {slot_data['generation_after_intra_settlement']:>15}")
            print(f"   Final Results:")
            print(f"     Grid Consumption:       {slot_data['final_grid_consumption']:>15}")
            print(f"     Surplus Generation:     {slot_data['final_surplus_generation']:>15}")
            print(f"   Settlements:")
            print(f"     Intra-TOD:              {slot_data['total_intra_settlement']:>15}")
            print(f"     Inter-TOD:              {slot_data['total_inter_settlement']:>15}")
    
    # Policy configuration
    policy = summary['policy_configuration']
    print(f"\n⚙️  POLICY CONFIGURATION:")
    print(f"   Policy:                   {policy['policy_name']}")
    print(f"   Intra-TOD Charges:        {policy['intra_tod_charge_percentage']}%")
    print(f"   Inter-TOD Charges:        {policy['inter_tod_charge_percentage']}%")


def display_batch_processing_results(results: Dict) -> None:
    """
    Display batch processing results.
    
    Args:
        results: Batch processing results dictionary
    """
    print("\n" + "=" * 100)
    print("BATCH PROCESSING RESULTS")
    print("=" * 100)
    
    print(f"\n📈 PROCESSING STATISTICS:")
    print(f"   Total Months Found:       {results['total_months_found']:>15}")
    print(f"   Successfully Processed:   {results['successfully_processed']:>15}")
    print(f"   Failed:                   {len(results['failed_months']):>15}")
    print(f"   Success Rate:             {results['overall_success_rate']:>14.1f}%")
    
    if results['failed_months']:
        print(f"\n❌ FAILED MONTHS:")
        for year, month in results['failed_months']:
            print(f"   - {year}-{month:02d}")
    
    print(f"\n📋 PROCESSING DETAILS:")
    for detail in results['monthly_processing_details']:
        status_emoji = "✅" if detail['status'] == 'SUCCESS' else "❌"
        print(f"   {status_emoji} {detail['year']}-{detail['month']:02d}: {detail['status']}")
        if detail['status'] != 'SUCCESS' and 'error' in detail:
            print(f"      Error: {detail['error']}")


def run_quick_demo():
    """
    Quick demonstration function for testing purposes.
    Run this if you want to quickly test with the first available month.
    """
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    # Database configuration
    DATABASE_URL = "mysql+pymysql://root:test123@localhost/energy_db"
    
    try:
        engine = create_engine(DATABASE_URL, echo=False)
        SessionLocal = sessionmaker(bind=engine)
        db_session = SessionLocal()
        
        # Create processor
        processor = create_karnataka_processor(db_session)
        
        # Get first available month
        available_months = processor.get_available_settlement_months()
        
        if available_months:
            target_year, target_month = available_months[0]
            logger.info(f"Running quick demo for {target_year}-{target_month:02d}")
            
            # Process and display results
            summary = demonstrate_karnataka_settlement(db_session, target_year, target_month)
            display_settlement_summary(summary)
        else:
            logger.error("No settlement data available for processing")
    
    except Exception as demo_error:
        logger.error(f"Error in quick demo: {str(demo_error)}")
    
    finally:
        if 'db_session' in locals():
            db_session.close()


if __name__ == "__main__":
    # Run the main interactive function
    main()
    
    # Alternatively, uncomment the line below for a quick demo:
    # run_quick_demo()