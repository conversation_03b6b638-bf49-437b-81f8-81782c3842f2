import pandas as pd
from datetime import datetime, time
def policy_banking(df):
    #db = client["Pipeline_App"]         # use your DB name
    #collection = db["banking_policy_metadata"]   # use your collection name
    policy_name ="Karnataka_2025"
    #doc = collection.find_one({"policy_name": policy_name})
    #if not doc:
        #raise ValueError(f"Policy '{policy_name}' not found in collection.")

    intra_tod_charges = 2
    inter_tod_charges = 8
    relationships = {
        "Peak-1": ["Peak-2","Normal","off-peak"],
        "Peak-2": [ "Peak-1","Normal","off-peak"],
        "Normal":["off-peak"]}
    time_slot_definition=  {
    '00:00': 'off-peak', '00:15': 'off-peak', '00:30': 'off-peak', '00:45': 'off-peak',
    '01:00': 'off-peak', '01:15': 'off-peak', '01:30': 'off-peak', '01:45': 'off-peak',
    '02:00': 'off-peak', '02:15': 'off-peak', '02:30': 'off-peak', '02:45': 'off-peak',
    '03:00': 'off-peak', '03:15': 'off-peak', '03:30': 'off-peak', '03:45': 'off-peak',
    '04:00': 'off-peak', '04:15': 'off-peak', '04:30': 'off-peak', '04:45': 'off-peak',
    '05:00': 'off-peak', '05:15': 'off-peak', '05:30': 'off-peak', '05:45': 'off-peak',
    '06:00': 'Peak-1',   '06:15': 'Peak-1',   '06:30': 'Peak-1',   '06:45': 'Peak-1',
    '07:00': 'Peak-1',   '07:15': 'Peak-1',   '07:30': 'Peak-1',   '07:45': 'Peak-1',
    '08:00': 'Peak-1',   '08:15': 'Peak-1',   '08:30': 'Peak-1',   '08:45': 'Peak-1',
    '09:00': 'Normal',   '09:15': 'Normal',   '09:30': 'Normal',   '09:45': 'Normal',
    '10:00': 'Normal',   '10:15': 'Normal',   '10:30': 'Normal',   '10:45': 'Normal',
    '11:00': 'Normal',   '11:15': 'Normal',   '11:30': 'Normal',   '11:45': 'Normal',
    '12:00': 'Normal',   '12:15': 'Normal',   '12:30': 'Normal',   '12:45': 'Normal',
    '13:00': 'Normal',   '13:15': 'Normal',   '13:30': 'Normal',   '13:45': 'Normal',
    '14:00': 'Normal',   '14:15': 'Normal',   '14:30': 'Normal',   '14:45': 'Normal',
    '15:00': 'Normal',   '15:15': 'Normal',   '15:30': 'Normal',   '15:45': 'Normal',
    '16:00': 'Normal',   '16:15': 'Normal',   '16:30': 'Normal',   '16:45': 'Normal',
    '17:00': 'Normal',   '17:15': 'Normal',   '17:30': 'Normal',   '17:45': 'Normal',
    '18:00': 'Peak-2',   '18:15': 'Peak-2',   '18:30': 'Peak-2',   '18:45': 'Peak-2',
    '19:00': 'Peak-2',   '19:15': 'Peak-2',   '19:30': 'Peak-2',   '19:45': 'Peak-2',
    '20:00': 'Peak-2',   '20:15': 'Peak-2',   '20:30': 'Peak-2',   '20:45': 'Peak-2',
    '21:00': 'Peak-2',   '21:15': 'Peak-2',   '21:30': 'Peak-2',   '21:45': 'Peak-2',
    '22:00': 'off-peak', '22:15': 'off-peak', '22:30': 'off-peak', '22:45': 'off-peak',
    '23:00': 'off-peak', '23:15': 'off-peak', '23:30': 'off-peak', '23:45': 'off-peak',
}

    print("Intra TOD Charges:", intra_tod_charges)
    print("Inter TOD Charges:", inter_tod_charges)
    print("Relationships:", relationships)
    print("Time Slot Definition:", time_slot_definition)
    #print("DataFrame:",df.head())
    # Create analysis directory if it doesn't exist
    df.to_csv('./analysis/analysis_step1.csv', index=False)
    #df["Slot"] = df["time"].apply(lambda x: time_slot_definition[str(x[:5])])
    # Convert to datetime.time for comparison
    df['time_obj'] = pd.to_datetime(df['time'], format='%H:%M:%S').dt.time

    # Function to classify time into slots
    def get_slot(t):
        if time(6, 0,0) <= t < time(9, 0,0):
            return 'Peak-1'
        elif time(9, 0,0) <= t < time(18, 0,0):
            return 'Normal'
        elif time(18, 0,0) <= t < time(22, 0,0):
            return 'Peak-2'
        else:
            return 'off-peak'

    # Apply function to create slot column
    df['Slot'] = df['time_obj'].apply(get_slot)

    # Drop helper column if not needed
    df.drop(columns=['time_obj'], inplace=True)
    df = df[['id', 'plant_id', 'client_name', 'cons_unit', 'type', 'date', 'time', 'Slot', 'allocated_generation', 'consumption', 'deficit', 'surplus_demand', 'surplus_generation', 'settled']]
    df.to_csv('./analysis/analysis_step2.csv', index=False)
    df['date'] = pd.to_datetime(df['date'])
    df['Month'] = df['date'].dt.month
    slot_summary = df.groupby(['Month', 'Slot']).agg({
    'allocated_generation':'sum',
    'consumption': 'sum',
    'surplus_demand': 'sum',
    'surplus_generation': 'sum'
    }).reset_index()
    slot_summary.to_csv('./analysis/analysis_step3_monthly.csv', index=False)

    #Intra TOD settlement
    intra_tod_charges =intra_tod_charges/100
    print("Intra TOD Charges (as fraction):", intra_tod_charges)
    def intra_tod_settlement(row,intra_tod_charges):
        demand = row['surplus_demand']
        lapsed = row['surplus_generation']
        if lapsed >= demand * (1+intra_tod_charges) :
        # Full settlement
            lapsed_after = lapsed - demand * (1+intra_tod_charges)
            demand_after = 0
            desc = f"Full Intra-TOD settlement: {demand:.2f} kWh demand settled using {demand*(1+intra_tod_charges):.2f} kWh lapsed."
        elif lapsed > 0:
            # Partial settlement
            settled = lapsed *(1-intra_tod_charges)
            demand_after = demand - settled
            lapsed_after = 0
            desc = f"Partial Intra-TOD settlement: {settled:.2f} kWh demand settled using all {lapsed:.2f} kWh lapsed, {demand_after:.2f} kWh remains."
        else:
            demand_after = demand
            lapsed_after = lapsed
            desc = "No Intra-TOD settlement."
        return pd.Series([demand_after, lapsed_after, desc])
    slot_summary[['Leftover Demand (kWh)_after_Intra_TOD', 'Lapsed_after_Intra_TOD', 'Intra_TOD_Settlement_desc']] = slot_summary.apply(
    lambda row: intra_tod_settlement(row, intra_tod_charges), axis=1
    )
    slot_summary.to_csv('./analysis/analysis_step4_intra.csv', index=False)
    
    # Inter TOD settlement
    inter_tod_charges = inter_tod_charges/100
    def get_month_dict(month, df):
        month_df = df[df['Month'] == month]
        result = {}
        for slot in ["Peak-1","Peak-2", "Normal","off-peak"]:
            row = month_df[month_df['Slot'].str.lower() == slot.lower()]
            if not row.empty:
                result[slot] = {
                    'Leftover Demand (kWh)': float(row['Leftover Demand (kWh)_after_Intra_TOD'].values[0]),
                    'Lapsed': float(row['Lapsed_after_Intra_TOD'].values[0])
                }
        return result
    def settle_lapsed(month_dict):
        settlement_desc = {slot: [] for slot in month_dict}
        def settle(giver, receiver):
            if month_dict[giver]['Lapsed'] > 0 and month_dict[receiver]['Leftover Demand (kWh)'] > 0:
                needed = month_dict[receiver]['Leftover Demand (kWh)']
                available = month_dict[giver]['Lapsed']
                if available >= needed * (1+inter_tod_charges):
                    msg = f"{receiver} demand of {needed:.2f} kWh fully settled by {giver} using {needed*(1+inter_tod_charges):.2f} kWh lapsed from {giver}"
                    settlement_desc[receiver].append(msg)
                    month_dict[giver]['Lapsed'] -= needed * (1+inter_tod_charges)
                    month_dict[receiver]['Leftover Demand (kWh)'] = 0
                else:
                    settled = available * (1-intra_tod_charges)
                    msg = f"{receiver} demand of {needed:.2f} kWh partially settled by {giver} using all {available:.2f} kWh lapsed from {giver} (settles {settled:.2f} kWh), {needed-settled:.2f} kWh remains"
                    settlement_desc[receiver].append(msg)
                    month_dict[receiver]['Leftover Demand (kWh)'] -= settled
                    month_dict[giver]['Lapsed'] = 0
        for giver, receivers in relationships.items():
            for receiver in receivers:
                settle(giver, receiver)
                #print(f"Settling {giver} to {receiver}")
        return month_dict, settlement_desc
    for month in range(1,len(slot_summary.Month.unique())):
        print("Month",month)
        month_dict = get_month_dict(month, slot_summary)
        print(month_dict)
        settled_dict, settlement_desc = settle_lapsed(month_dict)
        for slot in settled_dict:
            mask = (slot_summary['Month'] == month) & (slot_summary['Slot'].str.lower() == slot.lower())
            slot_summary.loc[mask, 'Leftover Demand (kWh)_after_inter_TOD_Settlement'] = settled_dict[slot]['Leftover Demand (kWh)']
            slot_summary.loc[mask, 'Lapsed_after_inter_TOD_Settlement'] = settled_dict[slot]['Lapsed']
            desc = "\n".join(settlement_desc[slot]) if settlement_desc[slot] else ""
            slot_summary.loc[mask, 'Settlement_desc'] = desc

    # Save results
    slot_summary.to_csv('./analysis/analysis_step5_inter.csv', index=False)
    slot_summary[['Month', 'Slot', 'Settlement_desc', 'Intra_TOD_Settlement_desc']].to_csv('./analysis/settlement_descriptions.csv', index=False)
    print(slot_summary[['Month', 'Slot', 'surplus_demand', 'surplus_generation', 'Leftover Demand (kWh)_after_Intra_TOD', 'Lapsed_after_Intra_TOD', 'Leftover Demand (kWh)_after_inter_TOD_Settlement', 'Lapsed_after_inter_TOD_Settlement', 'Intra_TOD_Settlement_desc', 'Settlement_desc']].head())
    full_summary = slot_summary.groupby(['Month']).agg({
        'allocated_generation':'sum',
        'consumption':'sum',
        'Leftover Demand (kWh)_after_inter_TOD_Settlement': 'sum',
        'Lapsed_after_inter_TOD_Settlement': 'sum'
    }).reset_index()
    full_summary['replacement']=(full_summary['consumption']-full_summary['Leftover Demand (kWh)_after_inter_TOD_Settlement'])
    full_summary.rename(columns={'Leftover Demand (kWh)_after_inter_TOD_Settlement': 'Grid Consumption','Lapsed_after_inter_TOD_Settlement': 'Surplus Generation'}, inplace=True)
    full_summary.to_csv('./analysis/analysis_step6_summary.csv', index=False)
    return full_summary



df =pd.read_csv("settlement_data.csv")
policy_banking(df)

