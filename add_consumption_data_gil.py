import pandas as pd
import os
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import TblConsumption, Base

def add_consumption_data():
    """
    Add consumption data from CSV file to the database.
    
    This script reads the 'Consumption data Cloud nine - processed_data.csv' file,
    processes the data, and inserts it into the tbl_consumption table.
    """
    # File path
    csv_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                           '15min_interval_data_May.csv')
    
    # Read CSV file
    print(f"Reading consumption data from {csv_file}...")
    df = pd.read_csv(csv_file)
    
    # Create database connection
    engine = create_engine("mysql+pymysql://root:test123@localhost/energy_db")
    
    # Create tables if they don't exist
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Process each row in the CSV
    records_added = 0
    records_skipped = 0
    
    print("Processing consumption data...")
    for index, row in df.iterrows():
        # Extract data from CSV
        cons_unit = row['R.R.No']
        client_name = row['Consumer']
        
        # Parse date and time
        time_str = row['time']
        consumption = row['Energy_kWh']
        
        # Convert time string to datetime
        # Handle ISO 8601 format with timezone (e.g., '2025-01-01T05:30:00Z+05:30')
        try:
            # First try the original format
            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # If that fails, try ISO 8601 format with timezone
            # Remove timezone info and parse the datetime part
            if 'T' in time_str:
                # Split at 'Z' or '+' to remove timezone info
                if 'Z+' in time_str:
                    datetime_part = time_str.split('Z')[0]
                elif 'Z-' in time_str:
                    datetime_part = time_str.split('Z')[0]
                elif '+' in time_str and 'T' in time_str:
                    datetime_part = time_str.split('+')[0]
                elif '-' in time_str and time_str.count('-') > 2:  # More than 2 dashes means timezone
                    # Find the last occurrence of '-' for timezone
                    parts = time_str.rsplit('-', 1)
                    datetime_part = parts[0]
                else:
                    datetime_part = time_str
                
                # Parse the datetime part
                dt = datetime.strptime(datetime_part, '%Y-%m-%dT%H:%M:%S')
            else:
                raise ValueError(f"Unable to parse datetime format: {time_str}")
        except ValueError as e:
            print(f"Error parsing datetime at row {index + 1}: '{time_str}'")
            print(f"Error details: {e}")
            continue
        
        date = dt.date()
        time = dt.time()
        
        # Check if record already exists
        existing = session.query(TblConsumption).filter_by(
            cons_unit=cons_unit,
            date=date,
            time=time
        ).first()
        
        if not existing:
            # Create new consumption record
            new_consumption = TblConsumption(
                cons_unit=cons_unit,
                client_name=client_name,
                date=date,
                time=time,
                consumption=consumption
            )
            session.add(new_consumption)
            records_added += 1
        else:
            records_skipped += 1
    
    # Commit changes
    session.commit()
    session.close()
    
    print(f"Consumption data import completed:")
    print(f"- {records_added} new records added")
    print(f"- {records_skipped} records skipped (already exist)")

if __name__ == "__main__":
    add_consumption_data()