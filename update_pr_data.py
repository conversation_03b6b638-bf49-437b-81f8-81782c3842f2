"""
Script to update existing tbl_generation records with PR, POA, and wind speed data
This script reads the calculated average PR data and updates the database records
"""
import pandas as pd
import numpy as np
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from models import TblGeneration
from db_setup import SessionLocal
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_pr_data_from_csv():
    """
    Update existing tbl_generation records with PR data from CSV file
    """
    try:
        # Read the CSV file with PR data
        csv_file_path = "scada_combined_data_with_avg_pr.csv"
        logger.info(f"Reading CSV file: {csv_file_path}")
        
        df = pd.read_csv(csv_file_path)
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Create session
        session = SessionLocal()
        
        # Process each row
        updated_count = 0
        not_found_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # Parse the datetime
                datetime_str = row['Date & Time']
                if pd.isna(datetime_str):
                    logger.debug(f"Skipping row {index} due to missing datetime")
                    continue
                
                try:
                    dt = datetime.strptime(datetime_str, '%d-%m-%Y %H:%M')
                except (ValueError, TypeError) as e:
                    logger.debug(f"Skipping row {index} due to invalid datetime format: {datetime_str}")
                    continue
                
                # Get PR, POA, and wind speed values
                pr_value = None
                if 'Average_PR' in row and not pd.isna(row['Average_PR']):
                    try:
                        pr_value = float(row['Average_PR'])
                    except (ValueError, TypeError):
                        pr_value = None
                
                poa_value = None
                if 'POA' in row and not pd.isna(row['POA']):
                    try:
                        poa_value = float(row['POA'])
                    except (ValueError, TypeError):
                        poa_value = None
                elif 'POA_Irradiance' in row and not pd.isna(row['POA_Irradiance']):
                    try:
                        poa_value = float(row['POA_Irradiance'])
                    except (ValueError, TypeError):
                        poa_value = None
                
                avg_wind_speed_value = None
                if 'Avg_Wind_Speed' in row and not pd.isna(row['Avg_Wind_Speed']):
                    try:
                        avg_wind_speed_value = float(row['Avg_Wind_Speed'])
                    except (ValueError, TypeError):
                        avg_wind_speed_value = None
                elif 'Wind_Speed' in row and not pd.isna(row['Wind_Speed']):
                    try:
                        avg_wind_speed_value = float(row['Wind_Speed'])
                    except (ValueError, TypeError):
                        avg_wind_speed_value = None
                
                # Get plant info if available
                plant_id = row.get('plant_id', 'IN.INTE.KIDS')  # Default to existing plant
                plant_type = row.get('type', 'solar')  # Default to solar
                
                # Find matching generation record(s)
                matching_records = session.query(TblGeneration).filter_by(
                    plant_id=plant_id,
                    date=dt.date(),
                    time=dt.time(),
                    type=plant_type
                ).all()
                
                if not matching_records:
                    # Try to find records with just date and time if plant_id/type not found
                    matching_records = session.query(TblGeneration).filter_by(
                        date=dt.date(),
                        time=dt.time()
                    ).all()
                
                if matching_records:
                    for record in matching_records:
                        # Update the record with new values
                        record.pr = pr_value
                        record.poa = poa_value
                        record.avg_wind_speed = avg_wind_speed_value
                        updated_count += 1
                else:
                    not_found_count += 1
                    logger.debug(f"No matching record found for {datetime_str}")
                
                # Commit every 100 records for better performance
                if (updated_count + not_found_count) % 100 == 0:
                    session.commit()
                    logger.info(f"Processed {updated_count + not_found_count} rows, updated {updated_count} records...")
                    
            except Exception as e:
                logger.error(f"Error processing row {index}: {e}")
                error_count += 1
                continue
        
        # Final commit
        session.commit()
        logger.info(f"Update completed successfully!")
        logger.info(f"Total records updated: {updated_count}")
        logger.info(f"Records not found in database: {not_found_count}")
        logger.info(f"Errors encountered: {error_count}")
        
    except Exception as e:
        logger.error(f"Error in update_pr_data_from_csv: {e}")
        if 'session' in locals():
            session.rollback()
        raise
    finally:
        if 'session' in locals():
            session.close()

def verify_updated_data():
    """
    Verify the updated data by checking a few records
    """
    try:
        session = SessionLocal()
        
        # Get count of records with PR data
        records_with_pr = session.query(TblGeneration).filter(
            TblGeneration.pr.isnot(None)
        ).count()
        
        records_with_poa = session.query(TblGeneration).filter(
            TblGeneration.poa.isnot(None)
        ).count()
        
        records_with_wind = session.query(TblGeneration).filter(
            TblGeneration.avg_wind_speed.isnot(None)
        ).count()
        
        total_records = session.query(TblGeneration).count()
        
        logger.info(f"Verification results:")
        logger.info(f"  - Total records: {total_records}")
        logger.info(f"  - Records with PR data: {records_with_pr}")
        logger.info(f"  - Records with POA data: {records_with_poa}")
        logger.info(f"  - Records with Wind Speed data: {records_with_wind}")
        
        # Get first few records with PR data
        sample_records = session.query(TblGeneration).filter(
            TblGeneration.pr.isnot(None)
        ).limit(5).all()
        
        logger.info("Sample records with PR data:")
        for record in sample_records:
            logger.info(f"  - Date: {record.date}, Time: {record.time}, "
                       f"Generation: {record.generation}, PR: {record.pr}, "
                       f"POA: {record.poa}, Wind Speed: {record.avg_wind_speed}")
            
    except Exception as e:
        logger.error(f"Error in verify_updated_data: {e}")
    finally:
        if 'session' in locals():
            session.close()

def check_available_columns():
    """
    Check what columns are available in the CSV files
    """
    try:
        # List of potential CSV files to check
        csv_files = [
            "scada_combined_data_with_avg_pr.csv",
            "scada_combined_data_with_pr.csv", 
            "scada_combined_data_latest_using.csv",
            "scada_combined_data.csv"
        ]
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, nrows=1)  # Just read the header
                logger.info(f"\nColumns in {csv_file}:")
                for i, col in enumerate(df.columns):
                    logger.info(f"  {i+1:2d}. {col}")
            except FileNotFoundError:
                logger.info(f"File not found: {csv_file}")
            except Exception as e:
                logger.error(f"Error reading {csv_file}: {e}")
                
    except Exception as e:
        logger.error(f"Error in check_available_columns: {e}")

if __name__ == "__main__":
    logger.info("Starting PR data update...")
    
    # First check what columns are available
    logger.info("Checking available columns in CSV files...")
    check_available_columns()
    
    # Update the data
    logger.info("\nUpdating PR data...")
    update_pr_data_from_csv()
    
    # Verify the update
    logger.info("\nVerifying updated data...")
    verify_updated_data()
    
    logger.info("Process completed!")