import pandas as pd
import os
from sqlalchemy import create_engine
from models import ConsumptionMapping, Base

def create_consumption_mapping():
    """
    Create consumption mapping entries from CSV file.
    
    This script reads the 'Consumption data Cloud nine - processed_data.csv' file,
    extracts unique combinations of R.R.No (cons_unit) and Division (location_name),
    and creates mapping entries in the database with a fixed percentage.
    """
    # File path
    csv_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                           'Consumption data Cloud nine - processed_data.csv')
    
    # Read CSV file
    df = pd.read_csv(csv_file)
    
    # Extract unique combinations of R.R.No and Division
    unique_mappings = df[['R.R.No', 'Division']].drop_duplicates()
    
    # Set fixed values
    client_name = "Kids Clinic India Limited"
    percentage = 10.00
    
    # Create database connection
    engine = create_engine("mysql+pymysql://root:test123@localhost/energy_db")
    
    # Create tables if they don't exist
    Base.metadata.create_all(engine)
    
    # Create session
    from sqlalchemy.orm import sessionmaker
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Process each unique mapping
    mappings_added = 0
    for _, row in unique_mappings.iterrows():
        cons_unit = row['R.R.No']
        location_name = row['Division']
        
        # Check if mapping already exists
        existing = session.query(ConsumptionMapping).filter_by(
            client_name=client_name,
            cons_unit=cons_unit
        ).first()
        
        if not existing:
            # Create new mapping
            new_mapping = ConsumptionMapping(
                client_name=client_name,
                cons_unit=cons_unit,
                location_name=location_name,
                percentage=percentage
            )
            session.add(new_mapping)
            mappings_added += 1
    
    # Commit changes
    session.commit()
    session.close()
    
    print(f"Consumption mapping process completed. Added {mappings_added} new mappings.")

if __name__ == "__main__":
    create_consumption_mapping()