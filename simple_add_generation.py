import pandas as pd
import numpy as np
from datetime import datetime
from models import TblGeneration
from db_setup import SessionLocal

def add_generation_data():
    """
    Simple function to add generation data from CSV to database
    """
    # Read the CSV file
    df = pd.read_csv("d:/<PERSON><PERSON><PERSON><PERSON><PERSON>/Data Ingestion/scada_combined_data_with_total.csv")
    
    # Create database session
    session = SessionLocal()
    
    try:
        for index, row in df.iterrows():
            # Parse datetime
            dt = datetime.strptime(row['Date & Time'], '%d-%m-%Y %H:%M')
            
            # Handle new columns - pr, poa, avg_wind_speed
            pr_value = None
            if 'Average_PR' in row and not pd.isna(row['Average_PR']):
                try:
                    pr_value = float(row['Average_PR'])
                except (ValueError, TypeError):
                    pr_value = None
            
            poa_value = None
            if 'POA' in row and not pd.isna(row['POA']):
                try:
                    poa_value = float(row['POA'])
                except (ValueError, TypeError):
                    poa_value = None
            
            avg_wind_speed_value = None
            if 'Avg_Wind_Speed' in row and not pd.isna(row['Avg_Wind_Speed']):
                try:
                    avg_wind_speed_value = float(row['Avg_Wind_Speed'])
                except (ValueError, TypeError):
                    avg_wind_speed_value = None
            
            # Create generation record
            generation_record = TblGeneration(
                plant_id=row['plant_id'],
                plant_name=row['plant_name'],
                client_name=row['client_name'],
                type=row['type'],
                date=dt.date(),
                time=dt.time(),
                generation=float(row['Total_Generation']),
                active_power=None,  # Not provided in CSV
                pr=pr_value,
                poa=poa_value,
                avg_wind_speed=avg_wind_speed_value
            )
            
            session.add(generation_record)
        
        # Commit all changes
        session.commit()
        print(f"Successfully inserted {len(df)} records into tbl_generation")
        
    except Exception as e:
        session.rollback()
        print(f"Error: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    add_generation_data()