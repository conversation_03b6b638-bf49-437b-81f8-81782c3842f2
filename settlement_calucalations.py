from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from decimal import Decimal
from models import SettlementData, Base  # adjust import as needed
from db_setup import session
# Step 1: Set up SQLAlchemy session


# Step 2: Define calculation logic
def get_time_slot(time_value):
    """
    Determine the time slot based on the time value
    Returns tuple of (slot_name, slot_time)
    """
    if time_value is None:
        return None, None
    
    # Convert time to hour for easier comparison
    hour = time_value.hour
    
    # Define time slots based on your requirements
    if 22 <= hour or hour < 6:  # 10pm to 6am (22:00 to 05:59)
        return "Night Off-Peak", "10pm to 6am"
    elif 6 <= hour < 9:  # 6am to 9am (06:00 to 08:59)
        return "Morning Peak", "6am to 9am"
    elif 9 <= hour < 18:  # 9am to 6pm (09:00 to 17:59)
        return "Day (Normal)", "9am to 6pm"
    elif 18 <= hour < 22:  # 6pm to 10pm (18:00 to 21:59)
        return "Evening Peak", "6pm to 10pm"
    else:
        return None, None

# def compute_settlement_metrics(row):
#     allocated = row.allocated_generation or Decimal("0.00")
#     consumed = row.consumption or Decimal("0.00")

#     row.settled = min(allocated, consumed)
#     row.deficit = allocated - consumed  # Positive when generation > consumption, negative when consumption > generation
#     row.surplus_demand = max(Decimal("0.00"), -row.deficit)  # Unmet demand when deficit is negative
#     row.surplus_generation = max(Decimal("0.00"), allocated - consumed)
    
#     # Update time slot information
#     slot_name, slot_time = get_time_slot(row.time)
#     row.slot_name = slot_name
#     row.slot_time = slot_time



def compute_settlement_metrics(row):
    """
    Compute solar energy settlement metrics for a given time period.
    
    Parameters:
    - allocated_generation: Solar energy allocated to this consumer
    - consumption: Energy consumed by the consumer
    
    Calculated metrics:
    - settled: Amount of allocated generation that was actually consumed
    - deficit: Shortfall in generation relative to consumption (positive = need more energy)
    - surplus_generation: Excess generation that wasn't consumed (can be sold back)
    - surplus_demand: Additional energy needed beyond what was allocated
    """
    allocated = row.allocated_generation or Decimal("0.00")
    consumed = row.consumption or Decimal("0.00")

    # Amount of allocated generation that was actually used
    row.settled = min(allocated, consumed)
    
    # Deficit: How much MORE energy is needed beyond what was allocated
    # Positive when consumption > allocated generation (energy shortfall)
    # Negative when allocated generation > consumption (excess generation)
    row.deficit = consumed - allocated
    
    # Surplus generation: Allocated energy that wasn't consumed (can be sold back)
    row.surplus_generation = max(Decimal("0.00"), allocated - consumed)
    
    # Surplus demand: Additional energy needed beyond allocated generation
    row.surplus_demand = max(Decimal("0.00"), consumed - allocated)
    
    # Update time slot information
    slot_name, slot_time = get_time_slot(row.time)
    row.slot_name = slot_name
    row.slot_time = slot_time

# Step 3: Query and update all rows
def update_settlement_data():
    records = session.query(SettlementData).all()
    for row in records:
        compute_settlement_metrics(row)
    
    session.commit()
    print(f"{len(records)} records updated.")

# Step 4: Run the update
update_settlement_data()
