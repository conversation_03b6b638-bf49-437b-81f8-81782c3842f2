import pandas as pd
from datetime import datetime

# Test date parsing with the sample format from CSV
test_date = "2025-06-01T00:00:00Z+05:30"
print(f"Original date string: {test_date}")

try:
    # Try robust date parsing - handle ISO format with timezone
    dt = pd.to_datetime(test_date, errors='coerce', utc=True)
    print(f"Parsed as UTC: {dt}")
    
    if not pd.isna(dt):
        # Convert to local timezone (IST) and make it timezone-naive for database storage
        dt = dt.tz_convert('Asia/Kolkata').tz_localize(None)
        print(f"Converted to IST and made naive: {dt}")
        print(f"Date: {dt.date()}")
        print(f"Time: {dt.time()}")
    else:
        print("Failed to parse datetime")
        
except Exception as e:
    print(f"Error: {e}")

# Test a few more samples
test_dates = [
    "2025-06-01T00:00:00Z+05:30",
    "2025-06-01T12:30:00Z+05:30",
    "2025-06-02T05:45:00Z+05:30"
]

print("\nTesting multiple dates:")
for test_date in test_dates:
    try:
        dt = pd.to_datetime(test_date, errors='coerce', utc=True)
        if not pd.isna(dt):
            dt = dt.tz_convert('Asia/Kolkata').tz_localize(None)
            print(f"{test_date} -> {dt} (Date: {dt.date()}, Time: {dt.time()})")
        else:
            print(f"Failed to parse: {test_date}")
    except Exception as e:
        print(f"Error parsing {test_date}: {e}")